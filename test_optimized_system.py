#!/usr/bin/env python3
"""
优化系统测试脚本
验证所有核心功能是否正常工作
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_database_connection():
    """测试数据库连接"""
    print("🗄️  测试数据库连接...")
    
    try:
        from app.database.db_session import get_db_session
        
        async with get_db_session() as db:
            # 简单的数据库查询测试
            result = await db.execute("SELECT 1")
            if result:
                print("✅ 数据库连接成功")
                return True
            else:
                print("❌ 数据库查询失败")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

async def test_character_generator():
    """测试角色生成器"""
    print("\n🎭 测试角色生成器...")
    
    try:
        from character_generator import CharacterGenerator
        
        generator = CharacterGenerator()
        
        # 测试生成一个简单角色
        character = await generator.generate_character(
            character_type="测试角色",
            additional_context="这是一个用于系统测试的虚拟角色"
        )
        
        if character and character.target_name:
            print(f"✅ 角色生成成功: {character.target_name}")
            print(f"   年龄: {character.age}")
            print(f"   职业: {character.occupation}")
            print(f"   大五人格: O={character.big_five.openness:.2f}")
            return True
        else:
            print("❌ 角色生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 角色生成器测试失败: {e}")
        return False

async def test_personality_simulator():
    """测试人格模拟器"""
    print("\n🤖 测试人格模拟器...")
    
    try:
        from app.services.personality_simulator import PersonalitySimulator
        
        simulator = PersonalitySimulator()
        
        if simulator.available:
            print("✅ 人格模拟器初始化成功")
            print("✅ LLM连接可用")
            return True
        else:
            print("⚠️  人格模拟器初始化成功，但LLM不可用")
            print("💡 请检查GEMINI_API_KEY环境变量")
            return False
            
    except Exception as e:
        print(f"❌ 人格模拟器测试失败: {e}")
        return False

async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        import httpx
        
        # 测试健康检查端点
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/")
                if response.status_code == 200:
                    print("✅ 健康检查端点正常")
                    return True
                else:
                    print(f"⚠️  健康检查端点返回状态码: {response.status_code}")
                    return False
            except httpx.ConnectError:
                print("⚠️  API服务器未启动")
                print("💡 请先运行: python start_optimized.py")
                return False
                
    except ImportError:
        print("⚠️  httpx未安装，跳过API测试")
        return False
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "backend/main.py",
        "backend/character_generator.py",
        "backend/batch_character_generator.py",
        "backend/character_manager.py",
        "backend/app/services/personality_simulator.py",
        "backend/app/database/models.py",
        "backend/requirements.txt",
        "docker-compose.yml",
        "start_optimized.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ 所有必需文件都存在")
        return True
    else:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False

def test_environment_config():
    """测试环境配置"""
    print("\n⚙️  测试环境配置...")
    
    env_file = Path("backend/.env")
    env_example = Path("backend/.env.example")
    
    if not env_example.exists():
        print("❌ .env.example文件不存在")
        return False
    
    if not env_file.exists():
        print("⚠️  .env文件不存在")
        print("💡 请复制.env.example为.env并配置API密钥")
        return False
    
    # 检查关键环境变量
    from dotenv import load_dotenv
    load_dotenv("backend/.env")
    
    gemini_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not gemini_key or gemini_key == "YOUR_GEMINI_API_KEY_HERE":
        print("⚠️  Gemini API Key未配置")
        print("💡 请在.env文件中设置GEMINI_API_KEY")
        return False
    
    print("✅ 环境配置检查通过")
    return True

async def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始系统综合测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 文件结构测试
    test_results.append(("文件结构", test_file_structure()))
    
    # 2. 环境配置测试
    test_results.append(("环境配置", test_environment_config()))
    
    # 3. 数据库连接测试
    test_results.append(("数据库连接", await test_database_connection()))
    
    # 4. 人格模拟器测试
    test_results.append(("人格模拟器", await test_personality_simulator()))
    
    # 5. 角色生成器测试
    test_results.append(("角色生成器", await test_character_generator()))
    
    # 6. API端点测试
    test_results.append(("API端点", await test_api_endpoints()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常")
        return True
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，请检查上述问题")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n📚 后续步骤:")
    print("=" * 40)
    print("1. 🚀 启动系统:")
    print("   python start_optimized.py")
    print()
    print("2. 🎭 生成测试角色:")
    print("   cd backend && python batch_character_generator.py")
    print()
    print("3. 💬 测试AI对话:")
    print("   cd backend && python character_manager.py")
    print()
    print("4. 🌐 查看API文档:")
    print("   http://localhost:8000/docs")
    print("=" * 40)

async def main():
    """主函数"""
    print("🎭 100% 人格复刻系统 - 优化版测试")
    print("专注于AI角色对话功能")
    print()
    
    try:
        # 运行综合测试
        success = await run_comprehensive_test()
        
        # 显示后续步骤
        show_next_steps()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
