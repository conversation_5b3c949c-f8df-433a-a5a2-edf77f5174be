#!/usr/bin/env python3
"""
完整系统测试脚本 - 验证西牟拉胡协议的所有功能
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class SystemTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.user_id = None
        self.personality_id = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_user_registration(self):
        """测试用户注册"""
        print("🔐 测试用户注册...")
        
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test123456"
        }
        
        async with self.session.post(f"{BASE_URL}/register", json=user_data) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.auth_token = data.get("access_token")
                print("✅ 用户注册成功")
                return True
            else:
                print(f"❌ 用户注册失败: {resp.status}")
                return False
    
    async def test_user_login(self):
        """测试用户登录"""
        print("🔑 测试用户登录...")
        
        login_data = {
            "username": "test_user",
            "password": "test123456"
        }
        
        async with self.session.post(f"{BASE_URL}/login", data=login_data) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.auth_token = data.get("access_token")
                print("✅ 用户登录成功")
                return True
            else:
                print(f"❌ 用户登录失败: {resp.status}")
                return False
    
    async def test_create_personality(self):
        """测试创建人格档案"""
        print("👤 测试创建人格档案...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        personality_data = {
            "target_name": "测试人格",
            "description": "这是一个用于测试西牟拉胡协议的人格档案"
        }
        
        async with self.session.post(
            f"{BASE_URL}/personalities", 
            json=personality_data, 
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.personality_id = data.get("profile_id")
                print(f"✅ 人格档案创建成功: {self.personality_id}")
                return True
            else:
                print(f"❌ 人格档案创建失败: {resp.status}")
                return False
    
    async def test_get_personality_detail(self):
        """测试获取人格档案详情"""
        print("📊 测试获取人格档案详情...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with self.session.get(
            f"{BASE_URL}/personalities/{self.personality_id}", 
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 人格档案详情获取成功: {data['target_name']}")
                print(f"   大五人格: {data['big_five']}")
                return True
            else:
                print(f"❌ 人格档案详情获取失败: {resp.status}")
                return False
    
    async def test_start_simulation(self):
        """测试启动AI模拟对话"""
        print("🤖 测试启动AI模拟对话...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        simulation_data = {
            "personality_id": self.personality_id,
            "initial_message": "你好，很高兴认识你！"
        }
        
        async with self.session.post(
            f"{BASE_URL}/api/v1/simulation/start/{self.personality_id}",
            json=simulation_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                conversation_id = data.get("conversation_id")
                ai_response = data.get("ai_response")
                print(f"✅ AI模拟对话启动成功")
                print(f"   对话ID: {conversation_id}")
                print(f"   AI回复: {ai_response}")
                return conversation_id
            else:
                text = await resp.text()
                print(f"❌ AI模拟对话启动失败: {resp.status}")
                print(f"   错误信息: {text}")
                return None
    
    async def test_continue_simulation(self, conversation_id: str):
        """测试继续AI模拟对话"""
        print("💬 测试继续AI模拟对话...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        message_data = {
            "user_input": "请告诉我你的人生故事"
        }
        
        async with self.session.post(
            f"{BASE_URL}/api/v1/simulation/chat/{conversation_id}",
            json=message_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                ai_response = data.get("ai_response")
                print(f"✅ AI模拟对话继续成功")
                print(f"   AI回复: {ai_response}")
                return True
            else:
                text = await resp.text()
                print(f"❌ AI模拟对话继续失败: {resp.status}")
                print(f"   错误信息: {text}")
                return False
    
    async def test_socratic_analysis(self):
        """测试苏格拉底式分析"""
        print("🧠 测试苏格拉底式分析...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        analysis_data = {
            "user_input": "我小时候很喜欢读书，经常一个人安静地看书到很晚。"
        }
        
        async with self.session.post(
            f"{BASE_URL}/chat/start/{self.personality_id}",
            json=analysis_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 苏格拉底式分析启动成功")
                print(f"   AI问题: {data.get('ai_response', 'N/A')}")
                return True
            else:
                text = await resp.text()
                print(f"❌ 苏格拉底式分析失败: {resp.status}")
                print(f"   错误信息: {text}")
                return False
    
    async def run_complete_test(self):
        """运行完整的系统测试"""
        print("🚀 开始完整系统测试...\n")
        
        # 测试用户认证
        if not await self.test_user_registration():
            # 如果注册失败，尝试登录
            if not await self.test_user_login():
                print("❌ 用户认证失败，测试终止")
                return False
        
        # 测试人格档案管理
        if not await self.test_create_personality():
            print("❌ 人格档案创建失败，测试终止")
            return False
        
        if not await self.test_get_personality_detail():
            print("❌ 人格档案详情获取失败")
        
        # 测试AI模拟功能
        conversation_id = await self.test_start_simulation()
        if conversation_id:
            await self.test_continue_simulation(conversation_id)
        
        # 测试分析功能
        await self.test_socratic_analysis()
        
        print("\n🎉 系统测试完成！")
        return True

async def main():
    """主函数"""
    async with SystemTester() as tester:
        await tester.run_complete_test()

if __name__ == "__main__":
    asyncio.run(main())
