"""
Enhanced FastAPI application for 100% personality cloning system
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from pydantic import BaseModel
from jose import JWTError, jwt
import structlog

from app.database.db_session import get_db_session, init_database, cleanup_database
from app.database.models import User, PersonalityProfile, Conversation, Message
# 导入模拟API
from app.api.endpoints import simulation

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Application lifecycle management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting personality cloning system")
    await init_database()
    yield
    # Shutdown
    logger.info("Shutting down personality cloning system")
    await cleanup_database()

# Create FastAPI app
app = FastAPI(
    title="100% Personality Cloning System",
    description="Advanced AI system for complete personality replication",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# --- JWT Configuration ---
SECRET_KEY = os.getenv("SECRET_KEY", "your-default-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
# --- End JWT Configuration ---

# Include API routers
app.include_router(simulation.router, prefix="/api/v1", tags=["Simulation"])

# === Pydantic Models ===

class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class PersonalityCreate(BaseModel):
    target_name: str
    description: Optional[str] = None

# 保留PersonalityCreate用于手动创建角色档案

# === Authentication ===

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """Get current authenticated user from JWT token"""
    token = credentials.credentials
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    result = await db.execute(select(User).where(User.username == username))
    user = result.scalar_one_or_none()

    if user is None or not user.is_active:
        raise credentials_exception

    return user

# === API Endpoints ===

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "100% Personality Cloning System is running",
        "version": "1.0.0",
        "status": "healthy"
    }

# 注册端点已删除，使用批量生成脚本创建演示用户

@app.post("/auth/login")
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db_session)
):
    """Login user"""
    try:
        # Find user
        result = await db.execute(
            select(User).where(User.username == login_data.username)
        )
        user = result.scalar_one_or_none()

        if not user or user.hashed_password != login_data.password:  # Simplified check
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        logger.info("User logged in", user_id=str(user.user_id))

        # 创建JWT Token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": str(user.user_id)},
            expires_delta=access_token_expires,
        )

        return {
            "message": "Login successful",
            "user_id": str(user.user_id),
            "token": access_token,  # 返回JWT Token
            "token_type": "bearer",
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.post("/personalities")
async def create_personality(
    personality_data: PersonalityCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Create a new personality profile"""
    try:
        new_personality = PersonalityProfile(
            user_id=current_user.user_id,
            target_name=personality_data.target_name,
            description=personality_data.description
        )
        
        db.add(new_personality)
        await db.commit()
        await db.refresh(new_personality)
        
        logger.info(
            "Personality profile created",
            personality_id=str(new_personality.profile_id),
            target_name=personality_data.target_name
        )
        
        return {
            "message": "Personality profile created",
            "personality_id": str(new_personality.profile_id),
            "target_name": personality_data.target_name,
            "completion_percentage": 0.0
        }
        
    except Exception as e:
        logger.error("Personality creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create personality profile"
        )

@app.get("/personalities")
async def list_personalities(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """List user's personality profiles"""
    try:
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.user_id == current_user.user_id)
        )
        personalities = result.scalars().all()
        
        return [
            {
                "personality_id": str(p.profile_id),
                "target_name": p.target_name,
                "description": p.description,
                "completion_percentage": p.completion_percentage,
                "created_at": p.created_at.isoformat() if p.created_at else None
            }
            for p in personalities
        ]
        
    except Exception as e:
        logger.error("Failed to list personalities", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve personality profiles"
        )

@app.get("/personalities/{personality_id}")
async def get_personality_detail(
    personality_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """获取单个人格档案的完整详情"""
    try:
        result = await db.execute(
            select(PersonalityProfile)
            .where(
                PersonalityProfile.profile_id == personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
            .options(
                selectinload(PersonalityProfile.events),
                selectinload(PersonalityProfile.beliefs),
                selectinload(PersonalityProfile.entities),
                selectinload(PersonalityProfile.family_members)
            )
        )
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Personality profile not found")

        # 将数据格式化为前端需要的结构
        return {
            "profile_id": str(profile.profile_id),
            "target_name": profile.target_name,
            "description": profile.description,
            "big_five": {
                "openness": profile.openness_score or 0.5,
                "conscientiousness": profile.conscientiousness_score or 0.5,
                "extraversion": profile.extraversion_score or 0.5,
                "agreeableness": profile.agreeableness_score or 0.5,
                "neuroticism": profile.neuroticism_score or 0.5,
            },
            "attachment_style": profile.attachment_style,
            "cultural_background": profile.cultural_background,
            "events": [
                {"title": e.title, "age": e.age_at_event, "narrative": e.full_narrative}
                for e in profile.events
            ],
            "beliefs": [
                {"statement": b.statement, "explanation": b.full_explanation}
                for b in profile.beliefs
            ],
            "family_members": [
                {"relationship": fm.relationship_type, "summary": fm.personality_summary}
                for fm in profile.family_members
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get personality detail", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve profile details")

# 用户对话相关的API端点已删除，现在只保留AI角色对话功能

# /chat/respond 端点已删除

# /predict 和 /validate 端点已删除，现在专注于AI角色对话功能

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
