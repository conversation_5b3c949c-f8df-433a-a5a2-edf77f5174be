#!/usr/bin/env python3
"""
自动化角色数据生成脚本
使用最新的Google GenAI SDK生成完整的角色人格数据并写入数据库
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from google import genai
from google.genai import types
from app.database.db_session import get_db_session, init_database
from app.database.models import (
    PersonalityProfile, Entity, Belief, Event, CognitivePattern, 
    LanguagePattern, EmotionalResponse, FamilyMember, User,
    CognitiveStyle, EmotionalState
)
import structlog

# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

# === Pydantic 数据模型 ===

class BigFivePersonality(BaseModel):
    """大五人格模型"""
    openness: float = Field(..., ge=0.0, le=1.0, description="开放性")
    conscientiousness: float = Field(..., ge=0.0, le=1.0, description="尽责性")
    extraversion: float = Field(..., ge=0.0, le=1.0, description="外向性")
    agreeableness: float = Field(..., ge=0.0, le=1.0, description="宜人性")
    neuroticism: float = Field(..., ge=0.0, le=1.0, description="神经质")

class CommunicationStyle(BaseModel):
    """沟通风格"""
    average_response_length: float = Field(..., ge=0.1, le=5.0, description="平均回复长度倍数")
    vocabulary_complexity: float = Field(..., ge=0.0, le=1.0, description="词汇复杂度")
    emotional_expressiveness: float = Field(..., ge=0.0, le=1.0, description="情感表达度")

class CognitiveTraits(BaseModel):
    """认知特征"""
    dominant_style: str = Field(..., description="主导认知风格: analytical/intuitive/systematic/creative")
    decision_making_speed: float = Field(..., ge=0.0, le=1.0, description="决策速度")
    risk_tolerance: float = Field(..., ge=0.0, le=1.0, description="风险承受度")

class CharacterEntity(BaseModel):
    """角色相关实体"""
    name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型: person/place/concept/object")
    relationship_type: Optional[str] = Field(None, description="关系类型")
    emotional_valence: float = Field(..., ge=-1.0, le=1.0, description="情感效价")
    importance_score: float = Field(..., ge=0.0, le=1.0, description="重要性评分")
    profile: Dict[str, Any] = Field(default_factory=dict, description="详细档案")

class CharacterBelief(BaseModel):
    """角色信念"""
    statement: str = Field(..., description="信念陈述")
    category: str = Field(..., description="信念类别: moral/political/personal/religious/professional")
    conviction_strength: float = Field(..., ge=0.0, le=1.0, description="信念强度")
    flexibility_score: float = Field(..., ge=0.0, le=1.0, description="灵活性评分")
    origin_context: str = Field(..., description="信念形成背景")
    full_explanation: str = Field(..., description="详细解释")

class CharacterEvent(BaseModel):
    """角色重要事件"""
    title: str = Field(..., description="事件标题")
    age_at_event: int = Field(..., ge=0, le=120, description="事件发生时年龄")
    life_stage: str = Field(..., description="人生阶段: childhood/adolescence/young_adult/adult/elderly")
    event_type: str = Field(..., description="事件类型: achievement/trauma/relationship/career/education")
    emotional_impact: float = Field(..., ge=-1.0, le=1.0, description="情感影响")
    centrality_score: float = Field(..., ge=0.0, le=1.0, description="中心性评分")
    memory_vividness: float = Field(..., ge=0.0, le=1.0, description="记忆清晰度")
    lessons_learned: List[str] = Field(..., description="学到的教训")
    full_narrative: str = Field(..., description="完整叙述")

class FamilyMemberData(BaseModel):
    """家庭成员数据"""
    relationship_type: str = Field(..., description="关系类型: 父亲/母亲/兄长/姐姐等")
    name: str = Field(..., description="姓名")
    personality_summary: Dict[str, float] = Field(..., description="人格摘要")
    parenting_style: Optional[str] = Field(None, description="教养风格(仅父母)")

class CompleteCharacterProfile(BaseModel):
    """完整角色档案"""
    # 基本信息
    target_name: str = Field(..., description="角色姓名")
    description: str = Field(..., description="角色描述")
    age: int = Field(..., ge=16, le=100, description="当前年龄")
    gender: str = Field(..., description="性别")
    occupation: str = Field(..., description="职业")
    
    # 人格特征
    big_five: BigFivePersonality
    communication_style: CommunicationStyle
    cognitive_traits: CognitiveTraits
    
    # 身份背景
    attachment_style: str = Field(..., description="依恋风格: secure/anxious/avoidant/disorganized")
    cultural_background: Dict[str, str] = Field(..., description="文化背景")
    
    # 相关数据
    entities: List[CharacterEntity] = Field(..., min_items=5, max_items=15, description="相关实体")
    beliefs: List[CharacterBelief] = Field(..., min_items=3, max_items=10, description="核心信念")
    events: List[CharacterEvent] = Field(..., min_items=3, max_items=8, description="重要事件")
    family_members: List[FamilyMemberData] = Field(..., min_items=2, max_items=6, description="家庭成员")

class CharacterGenerator:
    """角色生成器"""
    
    def __init__(self):
        # 初始化 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
            raise ValueError("请设置有效的 GEMINI_API_KEY 或 GOOGLE_API_KEY 环境变量")
        
        genai.configure(api_key=api_key)
        self.client = genai.Client(api_key=api_key)
        logger.info("CharacterGenerator initialized successfully")
    
    def generate_character_prompt(self, character_type: str, additional_context: str = "") -> str:
        """生成角色创建提示词"""
        base_prompt = f"""
请创建一个详细的{character_type}角色档案。这个角色应该是一个真实、立体、有深度的人物。

要求：
1. 角色应该有明确的个性特征和行为模式
2. 背景故事要合理且有说服力
3. 人格特征要相互一致且符合心理学原理
4. 包含足够的细节以支持AI角色扮演

{additional_context}

请确保生成的角色数据完整、一致且富有人性化特征。
"""
        return base_prompt.strip()
    
    async def generate_character(
        self, 
        character_type: str = "现代都市青年", 
        additional_context: str = ""
    ) -> CompleteCharacterProfile:
        """生成完整的角色档案"""
        try:
            prompt = self.generate_character_prompt(character_type, additional_context)
            
            response = await self.client.aio.models.generate_content(
                model='gemini-2.0-flash-001',
                contents=prompt,
                config=types.GenerateContentConfig(
                    response_mime_type='application/json',
                    response_schema=CompleteCharacterProfile,
                    temperature=0.8,  # 增加创造性
                    max_output_tokens=4000,
                )
            )
            
            character_data = json.loads(response.text)
            character = CompleteCharacterProfile(**character_data)
            
            logger.info(
                "Character generated successfully",
                character_name=character.target_name,
                character_type=character_type
            )
            
            return character
            
        except Exception as e:
            logger.error("Failed to generate character", error=str(e))
            raise
    
    async def save_character_to_db(
        self, 
        character: CompleteCharacterProfile, 
        user_id: str
    ) -> str:
        """将角色数据保存到数据库"""
        try:
            async with get_db_session() as db:
                # 创建主要人格档案
                personality = PersonalityProfile(
                    user_id=user_id,
                    target_name=character.target_name,
                    description=character.description,
                    completion_percentage=100.0,  # 自动生成的角色完成度为100%
                    
                    # 大五人格
                    openness_score=character.big_five.openness,
                    conscientiousness_score=character.big_five.conscientiousness,
                    extraversion_score=character.big_five.extraversion,
                    agreeableness_score=character.big_five.agreeableness,
                    neuroticism_score=character.big_five.neuroticism,
                    
                    # 认知特征
                    dominant_cognitive_style=CognitiveStyle(character.cognitive_traits.dominant_style),
                    decision_making_speed=character.cognitive_traits.decision_making_speed,
                    risk_tolerance=character.cognitive_traits.risk_tolerance,
                    
                    # 沟通风格
                    average_response_length=character.communication_style.average_response_length,
                    vocabulary_complexity=character.communication_style.vocabulary_complexity,
                    emotional_expressiveness=character.communication_style.emotional_expressiveness,
                    
                    # 身份背景
                    attachment_style=character.attachment_style,
                    cultural_background=character.cultural_background
                )
                
                db.add(personality)
                await db.flush()  # 获取 personality.profile_id
                
                # 保存相关实体
                for entity_data in character.entities:
                    entity = Entity(
                        personality_id=personality.profile_id,
                        name=entity_data.name,
                        entity_type=entity_data.entity_type,
                        relationship_type=entity_data.relationship_type,
                        emotional_valence=entity_data.emotional_valence,
                        importance_score=entity_data.importance_score,
                        profile=entity_data.profile
                    )
                    db.add(entity)
                
                # 保存信念
                for belief_data in character.beliefs:
                    belief = Belief(
                        personality_id=personality.profile_id,
                        statement=belief_data.statement,
                        belief_category=belief_data.category,
                        conviction_strength=belief_data.conviction_strength,
                        flexibility_score=belief_data.flexibility_score,
                        origin_context=belief_data.origin_context,
                        full_explanation=belief_data.full_explanation
                    )
                    db.add(belief)
                
                # 保存重要事件
                for event_data in character.events:
                    event = Event(
                        personality_id=personality.profile_id,
                        title=event_data.title,
                        age_at_event=event_data.age_at_event,
                        life_stage=event_data.life_stage,
                        event_type=event_data.event_type,
                        emotional_impact=event_data.emotional_impact,
                        centrality_score=event_data.centrality_score,
                        memory_vividness=event_data.memory_vividness,
                        lessons_learned=event_data.lessons_learned,
                        full_narrative=event_data.full_narrative
                    )
                    db.add(event)
                
                # 保存家庭成员
                for family_data in character.family_members:
                    family_member = FamilyMember(
                        personality_id=personality.profile_id,
                        relationship_type=family_data.relationship_type,
                        name=family_data.name,
                        personality_summary=family_data.personality_summary,
                        parenting_style=family_data.parenting_style
                    )
                    db.add(family_member)
                
                await db.commit()
                
                logger.info(
                    "Character saved to database successfully",
                    personality_id=str(personality.profile_id),
                    character_name=character.target_name
                )
                
                return str(personality.profile_id)
                
        except Exception as e:
            logger.error("Failed to save character to database", error=str(e))
            raise

async def main():
    """主函数"""
    print("🎭 角色数据生成器")
    print("=" * 50)
    
    # 初始化数据库
    await init_database()
    
    generator = CharacterGenerator()
    
    # 获取用户输入
    character_type = input("请输入角色类型 (默认: 现代都市青年): ").strip() or "现代都市青年"
    additional_context = input("请输入额外背景信息 (可选): ").strip()
    user_id = input("请输入用户ID (默认: demo用户): ").strip() or "demo"
    
    try:
        print(f"\n🔄 正在生成 '{character_type}' 角色...")
        
        # 生成角色
        character = await generator.generate_character(character_type, additional_context)
        
        print(f"✅ 角色生成成功: {character.target_name}")
        print(f"   描述: {character.description}")
        print(f"   年龄: {character.age}")
        print(f"   职业: {character.occupation}")
        
        # 保存到数据库
        print("\n💾 正在保存到数据库...")
        personality_id = await generator.save_character_to_db(character, user_id)
        
        print(f"✅ 角色保存成功!")
        print(f"   角色ID: {personality_id}")
        print(f"   角色名称: {character.target_name}")
        
        # 显示详细信息
        print(f"\n📊 角色详细信息:")
        print(f"   大五人格: 开放性={character.big_five.openness:.2f}, "
              f"尽责性={character.big_five.conscientiousness:.2f}, "
              f"外向性={character.big_five.extraversion:.2f}")
        print(f"   认知风格: {character.cognitive_traits.dominant_style}")
        print(f"   相关实体数量: {len(character.entities)}")
        print(f"   核心信念数量: {len(character.beliefs)}")
        print(f"   重要事件数量: {len(character.events)}")
        print(f"   家庭成员数量: {len(character.family_members)}")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
