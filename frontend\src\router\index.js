import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Login = () => import('../views/auth/Login.vue')
const Register = () => import('../views/auth/Register.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const PersonalityList = () => import('../views/personality/PersonalityList.vue')
const PersonalityDetail = () => import('../views/personality/PersonalityDetail.vue')
const PersonalityCreate = () => import('../views/personality/PersonalityCreate.vue')
const ChatInterface = () => import('../views/chat/ChatInterface.vue')
const NotFound = () => import('../views/error/NotFound.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiresAuth: true,
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    component: PersonalityList,
    meta: {
      title: '人格档案',
      requiresAuth: true,
      icon: 'User'
    }
  },
  {
    path: '/personalities/create',
    name: 'PersonalityCreate',
    component: PersonalityCreate,
    meta: {
      title: '创建人格档案',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '创建档案' }
      ]
    }
  },
  {
    path: '/personalities/:id',
    name: 'PersonalityDetail',
    component: PersonalityDetail,
    meta: {
      title: '人格详情',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '详情' }
      ]
    }
  },
  {
    path: '/chat/:personalityId?',
    name: 'ChatInterface',
    component: ChatInterface,
    meta: {
      title: 'AI角色对话',
      requiresAuth: true,
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      hideHeader: true,
      hideSidebar: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出菜单配置
export const menuRoutes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: {
      title: '仪表板',
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    meta: {
      title: '人格档案',
      icon: 'User'
    }
  },
  {
    path: '/chat',
    name: 'ChatInterface',
    meta: {
      title: 'AI角色对话',
      icon: 'ChatDotRound'
    }
  }
]
