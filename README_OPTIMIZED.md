# 🎭 100% 人格复刻系统 - 优化版

基于最新AI技术的角色生成与对话系统，专注于AI角色扮演功能。

## 🚀 系统特性

### 核心功能
- **🤖 自动化角色生成**: 使用最新Google GenAI SDK自动生成完整的角色人格数据
- **🎭 AI角色对话**: 基于人格档案的真实角色扮演对话
- **📊 多维度建模**: 涵盖大五人格、认知风格、情感模式、价值体系等
- **💾 完整数据管理**: 角色创建、查看、测试、删除等全生命周期管理

### 技术架构
- **前端**: Vue 3 + Element Plus + Pinia
- **后端**: FastAPI + SQLAlchemy + Pydantic
- **数据库**: PostgreSQL (简化架构，专注核心功能)
- **AI模型**: Google Gemini 2.0 Flash (最新统一SDK)
- **容器化**: Docker (仅PostgreSQL)

## 📋 系统要求

### 必需软件
- Python 3.10+
- Docker Desktop
- Node.js 16+ (可选，用于前端)

### API密钥
- Google Gemini API Key (必需)

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd personality-clone-system
```

### 2. 环境配置
```bash
cd backend
cp .env.example .env
# 编辑 .env 文件，填入你的 Gemini API Key
```

### 3. 一键启动
```bash
python start_optimized.py
```

这个脚本会自动：
- 检查系统要求
- 启动PostgreSQL数据库
- 安装Python依赖
- 初始化数据库
- 提供启动后端服务的选项

### 4. 手动启动（可选）
如果需要手动控制：

```bash
# 启动数据库
docker-compose up -d postgres

# 启动后端
cd backend
python -m venv venv
# Windows: venv\Scripts\activate
# macOS/Linux: source venv/bin/activate
pip install -r requirements.txt
python init_db.py
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 启动前端（可选）
cd frontend
npm install
npm run dev
```

## 🎯 核心功能使用

### 1. 自动生成角色
```bash
cd backend
python character_generator.py
```

支持自定义角色类型和背景描述，自动生成：
- 完整的人格特征（大五人格模型）
- 认知风格和沟通模式
- 核心信念和价值观
- 重要人生事件
- 家庭关系网络
- 相关人物和实体

### 2. 批量生成角色
```bash
cd backend
python batch_character_generator.py
```

预定义10种角色类型：
- 科技公司程序员
- 文艺青年
- 职场白领
- 大学生
- 创业者
- 教师
- 医护人员
- 艺术家
- 退休老人
- 家庭主妇

### 3. 角色管理
```bash
cd backend
python character_manager.py
```

提供完整的角色管理功能：
- 查看所有角色列表
- 查看角色详细信息
- 测试角色对话
- 删除角色

### 4. AI角色对话
通过API端点进行角色对话：

```bash
# 启动对话
POST /api/v1/simulation/start/{personality_id}
{
    "initial_message": "你好，很高兴认识你！"
}

# 继续对话
POST /api/v1/simulation/chat/{conversation_id}
{
    "user_input": "你今天心情怎么样？"
}
```

## 📊 API文档

启动后端服务后，访问：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000

### 主要API端点

#### 认证相关
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录

#### 角色管理
- `GET /personalities` - 获取角色列表
- `POST /personalities` - 创建角色档案
- `GET /personalities/{id}` - 获取角色详情

#### AI对话
- `POST /api/v1/simulation/start/{personality_id}` - 启动对话
- `POST /api/v1/simulation/chat/{conversation_id}` - 继续对话
- `GET /api/v1/simulation/conversations/{personality_id}` - 获取对话历史

## 🔧 开发指南

### 项目结构
```
personality-clone-system/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── database/          # 数据库模型和会话
│   │   ├── services/          # 业务逻辑服务
│   │   └── api/endpoints/     # API端点
│   ├── character_generator.py # 角色生成脚本
│   ├── batch_character_generator.py # 批量生成脚本
│   ├── character_manager.py   # 角色管理脚本
│   └── main.py               # FastAPI应用入口
├── frontend/                  # 前端应用（可选）
├── docker-compose.yml         # 数据库服务配置
└── start_optimized.py        # 优化启动脚本
```

### 核心服务

#### PersonalitySimulator
AI角色扮演的核心服务，负责：
- 加载完整的人格档案数据
- 计算动态情绪和关系状态
- 检索相关记忆和经历
- 生成符合角色特征的回复

#### CharacterGenerator
自动化角色生成服务，使用最新的Google GenAI SDK：
- 结构化JSON输出（Pydantic模式）
- 系统指令优化
- 创造性参数调优
- 完整数据验证

## 🎨 角色数据模型

### 基础信息
- 姓名、年龄、性别、职业
- 角色描述和背景

### 人格特征
- **大五人格**: 开放性、尽责性、外向性、宜人性、神经质
- **认知风格**: 分析型、直觉型、系统型、创造型
- **沟通模式**: 回复长度、词汇复杂度、情感表达度

### 深度数据
- **核心信念**: 道德观、政治观、个人价值观
- **重要事件**: 关键人生经历和影响
- **人际关系**: 家庭成员、重要他人
- **相关实体**: 重要地点、概念、物品

## 🔒 安全考虑

- 简化的JWT认证机制
- 数据库连接安全
- API访问控制
- 环境变量保护

## 📈 性能优化

- 异步数据库操作
- 连接池管理
- 缓存策略（会话状态）
- 批量数据处理

## 🚧 开发状态

当前版本为优化重构版本：
- ✅ 核心架构简化
- ✅ AI角色对话功能完善
- ✅ 自动化角色生成
- ✅ 最新SDK集成
- ✅ 批量管理工具
- 🚧 前端界面优化中
- 📋 高级功能规划中

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看文档
2. 搜索已有Issues
3. 创建新Issue

---

**注意**: 这是一个专注于AI角色对话的优化版本，移除了用户人格分析功能，专注于提供高质量的AI角色扮演体验。
