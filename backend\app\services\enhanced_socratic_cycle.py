"""
Enhanced Socratic cycle for 100% personality cloning
Implements THINK -> ASK -> ANALYZE -> PREDICT -> VALIDATE cycle
"""

import json
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from app.services.personality_analyzer import PersonalityAnalyzer
from app.database.models import (
    PersonalityProfile, Conversation, Message, User,
    Entity, Belief, Event, CognitivePattern, LanguagePattern, EmotionalResponse
)
from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    PersonalitySnapshot, SituationalResponse, PersonalitySimilarity
)
import structlog

logger = structlog.get_logger()

class EnhancedSocraticCycle:
    def __init__(self):
        self.analyzer = PersonalityAnalyzer()
    
    async def think_phase(
        self, 
        personality_id: str, 
        db: AsyncSession
    ) -> QuestioningStrategy:
        """
        Enhanced THINK phase: Analyze current personality state and plan next questions
        """
        try:
            # Get current personality profile
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality = result.scalar_one_or_none()
            
            if not personality:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get completion status
            completion_status = await self.analyzer.get_personality_completion_status(
                personality_id, db
            )
            
            # Collect current insights
            collected_insights = await self._collect_current_insights(personality_id, db)
            
            # Identify missing information areas
            missing_areas = self._identify_missing_areas(completion_status, collected_insights)
            
            # Get recent conversation history
            conversation_history = await self._get_recent_conversation_history(
                personality_id, db, limit=10
            )
            
            # Generate strategic questioning plan
            strategy = await self.analyzer.generate_strategic_questions(
                target_name=personality.target_name,
                completion_percentage=completion_status["completion_percentage"],
                collected_insights=collected_insights,
                current_focus_areas=missing_areas,
                missing_information=missing_areas,
                conversation_history=conversation_history
            )
            
            logger.info(
                "THINK phase completed",
                personality_id=personality_id,
                completion_percentage=completion_status["completion_percentage"],
                strategy_goal=strategy.overall_goal
            )
            
            return strategy
            
        except Exception as e:
            logger.error("THINK phase failed", error=str(e), personality_id=personality_id)
            raise

    async def ask_phase(
        self, 
        strategy: QuestioningStrategy, 
        conversation_history: List[str]
    ) -> EmpatheticQuestion:
        """
        Enhanced ASK phase: Generate empathetic question based on strategy
        """
        try:
            # Select the highest priority question from strategy
            if not strategy.questioning_strategy.strategic_questions:
                return EmpatheticQuestion(
                    question_text="请告诉我更多关于这个人的信息。",
                    question_context="通用询问",
                    expected_response_type="描述性"
                )
            
            priority_question = strategy.questioning_strategy.strategic_questions[0]
            
            # Generate empathetic version of the strategic question
            empathetic_question = await self.analyzer.generate_empathetic_question(
                strategic_goal=strategy.overall_goal,
                target_insight=priority_question.target_insight,
                conversation_history=conversation_history
            )
            
            logger.info(
                "ASK phase completed",
                question_type=priority_question.question_type,
                target_insight=priority_question.target_insight
            )
            
            return empathetic_question
            
        except Exception as e:
            logger.error("ASK phase failed", error=str(e))
            raise

    async def analyze_phase(
        self, 
        user_input: str, 
        personality_id: str,
        conversation_history: List[str],
        db: AsyncSession
    ) -> DeepAnalysisResult:
        """
        Enhanced ANALYZE phase: Deep personality analysis and database updates
        """
        try:
            # Get current personality state
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get current personality state as dict
            current_state = await self._get_personality_state_dict(personality_data, db)
            
            # Perform deep analysis
            analysis_result = await self.analyzer.analyze_personality_deep(
                user_narrative=user_input,
                conversation_history=conversation_history,
                target_name=personality_data.target_name,
                current_personality_state=current_state,
                db=db
            )
            
            # Execute database operations
            await self._execute_database_operations(
                analysis_result, personality_id, db
            )
            
            # Update personality completion percentage
            completion_status = await self.analyzer.get_personality_completion_status(
                personality_id, db
            )
            personality_data.completion_percentage = completion_status["completion_percentage"]
            
            logger.info(
                "ANALYZE phase completed",
                personality_id=personality_id,
                confidence_score=analysis_result.confidence_score,
                new_insights_count=len(analysis_result.new_insights)
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error("ANALYZE phase failed", error=str(e), personality_id=personality_id)
            raise

    async def predict_phase(
        self,
        personality_id: str,
        situation: str,
        db: AsyncSession
    ) -> SituationalResponse:
        """
        PREDICT phase: Predict personality response to given situation
        """
        try:
            # Get personality profile
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get complete personality state
            personality_state = await self._get_personality_state_dict(personality_data, db)
            
            # Generate prediction
            prediction = await self.analyzer.predict_personality_response(
                target_name=personality_data.target_name,
                personality_profile=personality_state,
                situation_description=situation
            )
            
            logger.info(
                "PREDICT phase completed",
                personality_id=personality_id,
                confidence=prediction.confidence
            )
            
            return prediction
            
        except Exception as e:
            logger.error("PREDICT phase failed", error=str(e), personality_id=personality_id)
            raise

    async def validate_phase(
        self,
        personality_id: str,
        reference_data: Optional[Dict[str, Any]],
        db: AsyncSession
    ) -> PersonalitySimilarity:
        """
        VALIDATE phase: Assess personality cloning accuracy
        """
        try:
            # Get constructed personality
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            constructed_personality = await self._get_personality_state_dict(personality_data, db)
            
            # Assess similarity
            similarity = await self.analyzer.assess_personality_similarity(
                target_name=personality_data.target_name,
                constructed_personality=constructed_personality,
                reference_personality=reference_data
            )
            
            logger.info(
                "VALIDATE phase completed",
                personality_id=personality_id,
                overall_similarity=similarity.overall_similarity
            )
            
            return similarity
            
        except Exception as e:
            logger.error("VALIDATE phase failed", error=str(e), personality_id=personality_id)
            raise

    # Helper methods
    
    async def _collect_current_insights(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """Collect current personality insights from database"""
        insights = {
            "entities": [],
            "beliefs": [],
            "events": [],
            "cognitive_patterns": [],
            "emotional_responses": []
        }
        
        # Get entities
        entities_result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        insights["entities"] = [
            {
                "name": entity.name,
                "type": entity.entity_type,
                "relationship": entity.relationship_type,
                "importance": entity.importance_score
            }
            for entity in entities_result.scalars().all()
        ]
        
        # Get beliefs
        beliefs_result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        insights["beliefs"] = [
            {
                "statement": belief.statement,
                "category": belief.belief_category,
                "conviction": belief.conviction_strength
            }
            for belief in beliefs_result.scalars().all()
        ]
        
        # Get events
        events_result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        insights["events"] = [
            {
                "title": event.title,
                "age": event.age_at_event,
                "type": event.event_type,
                "impact": event.emotional_impact
            }
            for event in events_result.scalars().all()
        ]
        
        return insights

    def _identify_missing_areas(
        self, 
        completion_status: Dict[str, Any], 
        insights: Dict[str, Any]
    ) -> List[str]:
        """Identify areas that need more exploration"""
        missing_areas = []
        
        # Check completion factors
        for area, score in completion_status.get("completion_factors", {}).items():
            if score < 0.7:
                missing_areas.append(area)
        
        # Check insight depth
        if len(insights.get("entities", [])) < 5:
            missing_areas.append("relationship_network")
        
        if len(insights.get("beliefs", [])) < 3:
            missing_areas.append("value_system")
        
        if len(insights.get("events", [])) < 5:
            missing_areas.append("life_experiences")
        
        return missing_areas

    async def _get_recent_conversation_history(
        self, 
        personality_id: str, 
        db: AsyncSession, 
        limit: int = 10
    ) -> List[str]:
        """Get recent conversation history"""
        messages_result = await db.execute(
            select(Message)
            .join(Conversation)
            .where(Conversation.personality_id == personality_id)
            .order_by(Message.timestamp.desc())
            .limit(limit)
        )
        
        messages = messages_result.scalars().all()
        return [f"{msg.sender}: {msg.content}" for msg in reversed(messages)]

    async def _get_personality_state_dict(
        self, 
        personality: PersonalityProfile, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Convert personality profile to dictionary"""
        return {
            "target_name": personality.target_name,
            "completion_percentage": personality.completion_percentage,
            "big_five": {
                "openness": personality.openness_score,
                "conscientiousness": personality.conscientiousness_score,
                "extraversion": personality.extraversion_score,
                "agreeableness": personality.agreeableness_score,
                "neuroticism": personality.neuroticism_score
            },
            "cognitive_style": personality.dominant_cognitive_style.value if personality.dominant_cognitive_style else None,
            "decision_speed": personality.decision_making_speed,
            "risk_tolerance": personality.risk_tolerance,
            "communication": {
                "response_length": personality.average_response_length,
                "vocabulary_complexity": personality.vocabulary_complexity,
                "emotional_expressiveness": personality.emotional_expressiveness
            }
        }

    async def _execute_database_operations(
        self, 
        analysis_result: DeepAnalysisResult, 
        personality_id: str, 
        db: AsyncSession
    ):
        """Execute database operations from analysis result"""
        try:
            # Execute PostgreSQL operations
            for op in analysis_result.postgres_operations:
                await self._execute_postgres_operation(op, personality_id, db)
            
            # Note: Neo4j and ChromaDB operations would be implemented here
            # For now, we focus on PostgreSQL operations
            
            await db.commit()
            
        except Exception as e:
            await db.rollback()
            logger.error("Database operations failed", error=str(e))
            raise

    async def _execute_postgres_operation(
        self,
        operation: Dict[str, Any],
        personality_id: str,
        db: AsyncSession
    ):
        """Execute a single PostgreSQL operation based on LLM analysis"""
        op_type = operation.get("type")
        op_data = operation.get("data")

        if not op_type or not op_data:
            logger.warning("Invalid database operation format", operation=operation)
            return

        try:
            if op_type == "add_belief":
                new_belief = Belief(
                    personality_id=personality_id,
                    statement=op_data.get("statement"),
                    belief_category=op_data.get("category", "personal"),
                    conviction_strength=op_data.get("conviction", 0.7),
                    flexibility_score=op_data.get("flexibility", 0.5),
                    origin_context=op_data.get("origin_context"),
                    full_explanation=op_data.get("explanation")
                )
                db.add(new_belief)
                logger.info("Added new belief", belief=op_data.get("statement"))

            elif op_type == "add_event":
                new_event = Event(
                    personality_id=personality_id,
                    title=op_data.get("title"),
                    age_at_event=op_data.get("age"),
                    life_stage=op_data.get("life_stage"),
                    event_type=op_data.get("event_type", "general"),
                    emotional_impact=op_data.get("emotional_impact", 0.0),
                    centrality_score=op_data.get("centrality", 0.5),
                    memory_vividness=op_data.get("vividness", 0.5),
                    lessons_learned=op_data.get("lessons", []),
                    full_narrative=op_data.get("narrative")
                )
                db.add(new_event)
                logger.info("Added new event", event=op_data.get("title"))

            elif op_type == "add_entity":
                new_entity = Entity(
                    personality_id=personality_id,
                    name=op_data.get("name"),
                    entity_type=op_data.get("entity_type", "person"),
                    relationship_type=op_data.get("relationship_type"),
                    emotional_valence=op_data.get("emotional_valence", 0.0),
                    importance_score=op_data.get("importance", 0.5),
                    profile=op_data.get("profile", {})
                )
                db.add(new_entity)
                logger.info("Added new entity", entity=op_data.get("name"))

            elif op_type == "update_big_five":
                result = await db.execute(
                    select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
                )
                profile = result.scalar_one_or_none()
                if profile:
                    profile.openness_score = op_data.get("openness", profile.openness_score)
                    profile.conscientiousness_score = op_data.get("conscientiousness", profile.conscientiousness_score)
                    profile.extraversion_score = op_data.get("extraversion", profile.extraversion_score)
                    profile.agreeableness_score = op_data.get("agreeableness", profile.agreeableness_score)
                    profile.neuroticism_score = op_data.get("neuroticism", profile.neuroticism_score)
                    logger.info("Updated Big Five scores", profile_id=personality_id)

            elif op_type == "add_family_member":
                # 导入FamilyMember模型
                from app.database.models import FamilyMember
                new_family_member = FamilyMember(
                    personality_id=personality_id,
                    relationship_type=op_data.get("relationship"),
                    name=op_data.get("name"),
                    personality_summary=op_data.get("personality_summary", {}),
                    parenting_style=op_data.get("parenting_style")
                )
                db.add(new_family_member)
                logger.info("Added family member", relationship=op_data.get("relationship"))

            elif op_type == "update_attachment_style":
                result = await db.execute(
                    select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
                )
                profile = result.scalar_one_or_none()
                if profile:
                    profile.attachment_style = op_data.get("attachment_style", profile.attachment_style)
                    logger.info("Updated attachment style", style=op_data.get("attachment_style"))

            else:
                logger.warning("Unknown operation type", op_type=op_type)

        except Exception as e:
            logger.error("Failed to execute postgres operation", error=str(e), operation=operation)
            raise
