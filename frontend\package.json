{"name": "personality-clone-frontend", "version": "1.0.0", "description": "Frontend for 100% Personality Cloning System", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "d3": "^7.8.5", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "marked": "^9.1.6", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@types/d3": "^7.4.3", "@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "vite": "^5.0.0"}, "engines": {"node": ">=16.0.0"}}