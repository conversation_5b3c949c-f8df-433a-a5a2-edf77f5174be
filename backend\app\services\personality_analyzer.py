"""
简化的人格分析服务
仅保留AI角色对话所需的基本功能
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from google import genai
from google.genai import types
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from dotenv import load_dotenv

from app.database.models import PersonalityProfile

load_dotenv()

class PersonalityAnalyzer:
    """简化的人格分析器，仅用于支持AI角色对话"""

    def __init__(self):
        # Initialize Google GenAI client (new SDK)
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False

    async def get_personality_completion_status(
        self,
        personality_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """计算人格档案完成状态（仅用于显示）"""

        # Query database for current personality data
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()

        if not personality:
            return {"completion_percentage": 0.0, "missing_areas": []}

        # Calculate completion based on available data
        completion_factors = {
            "basic_info": 1.0 if personality.target_name else 0.0,
            "big_five": sum([
                1.0 if personality.openness_score != 0.5 else 0.0,
                1.0 if personality.conscientiousness_score != 0.5 else 0.0,
                1.0 if personality.extraversion_score != 0.5 else 0.0,
                1.0 if personality.agreeableness_score != 0.5 else 0.0,
                1.0 if personality.neuroticism_score != 0.5 else 0.0,
            ]) / 5.0,
            "cognitive_style": 1.0 if personality.dominant_cognitive_style else 0.0,
            "communication": 1.0 if personality.average_response_length else 0.0,
        }

        overall_completion = sum(completion_factors.values()) / len(completion_factors) * 100

        missing_areas = [
            area for area, score in completion_factors.items() if score < 0.8
        ]

        return {
            "completion_percentage": overall_completion,
            "missing_areas": missing_areas,
            "completion_factors": completion_factors
        }

