# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
aiosqlite==0.19.0
alembic==1.13.1

# Graph database
neo4j==5.15.0

# Vector database
chromadb==0.4.18

# Cache
redis==5.0.1

# Search
elasticsearch==8.11.0

# AI/ML
google-genai>=0.3.0  # 最新的统一Google GenAI SDK
google-generativeai==0.8.0  # 保留旧版本以兼容现有代码
openai==1.54.0
instructor==1.6.0
transformers==4.46.0
torch>=2.6.0
sentence-transformers==3.3.0
numpy>=1.24.0

# Text analysis
textstat==0.7.3
spacy==3.7.2
nltk==3.8.1

# Audio analysis (optional)
azure-cognitiveservices-speech==1.34.0
librosa==0.10.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
httpx==0.25.2
aiofiles==23.2.1
python-dateutil==2.8.2

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
mypy==1.7.1
