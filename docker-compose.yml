services:
  postgres:
    image: postgres:16
    container_name: personality_clone_postgres
    environment:
      POSTGRES_USER: user # 注意：生产环境应使用更安全的凭证
      POSTGRES_PASSWORD: password # 注意：生产环境应使用更安全的凭证
      POSTGRES_DB: personality_clone_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
