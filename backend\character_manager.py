#!/usr/bin/env python3
"""
角色管理脚本
用于查看、管理和测试生成的角色数据
"""

import asyncio
import sys
import os
from typing import List, Dict, Any, Optional
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.db_session import get_db_session
from app.database.models import (
    PersonalityProfile, Entity, Belief, Event, 
    FamilyMember, User, Conversation, Message
)
from app.services.personality_simulator import PersonalitySimulator
from sqlalchemy.future import select
from sqlalchemy import func, desc
import structlog

logger = structlog.get_logger()

class CharacterManager:
    """角色管理器"""
    
    def __init__(self):
        self.simulator = PersonalitySimulator()
    
    async def list_all_characters(self) -> List[Dict[str, Any]]:
        """列出所有角色"""
        try:
            async with get_db_session() as db:
                result = await db.execute(
                    select(PersonalityProfile, User.username)
                    .join(User, PersonalityProfile.user_id == User.user_id)
                    .order_by(desc(PersonalityProfile.created_at))
                )
                
                characters = []
                for personality, username in result.all():
                    characters.append({
                        "id": str(personality.profile_id),
                        "name": personality.target_name,
                        "description": personality.description,
                        "completion": personality.completion_percentage,
                        "created_at": personality.created_at.strftime("%Y-%m-%d %H:%M"),
                        "owner": username,
                        "big_five": {
                            "openness": personality.openness_score,
                            "conscientiousness": personality.conscientiousness_score,
                            "extraversion": personality.extraversion_score,
                            "agreeableness": personality.agreeableness_score,
                            "neuroticism": personality.neuroticism_score
                        }
                    })
                
                return characters
                
        except Exception as e:
            logger.error("Failed to list characters", error=str(e))
            raise
    
    async def get_character_details(self, personality_id: str) -> Dict[str, Any]:
        """获取角色详细信息"""
        try:
            async with get_db_session() as db:
                # 获取基本信息
                result = await db.execute(
                    select(PersonalityProfile).where(
                        PersonalityProfile.profile_id == personality_id
                    )
                )
                personality = result.scalar_one_or_none()
                
                if not personality:
                    raise ValueError(f"角色 {personality_id} 不存在")
                
                # 获取相关实体
                entities_result = await db.execute(
                    select(Entity).where(Entity.personality_id == personality_id)
                )
                entities = entities_result.scalars().all()
                
                # 获取信念
                beliefs_result = await db.execute(
                    select(Belief).where(Belief.personality_id == personality_id)
                )
                beliefs = beliefs_result.scalars().all()
                
                # 获取事件
                events_result = await db.execute(
                    select(Event).where(Event.personality_id == personality_id)
                )
                events = events_result.scalars().all()
                
                # 获取家庭成员
                family_result = await db.execute(
                    select(FamilyMember).where(FamilyMember.personality_id == personality_id)
                )
                family_members = family_result.scalars().all()
                
                return {
                    "personality": personality,
                    "entities": entities,
                    "beliefs": beliefs,
                    "events": events,
                    "family_members": family_members
                }
                
        except Exception as e:
            logger.error("Failed to get character details", error=str(e))
            raise
    
    async def test_character_conversation(
        self, 
        personality_id: str, 
        test_messages: List[str]
    ) -> List[Dict[str, str]]:
        """测试角色对话"""
        try:
            conversation_history = []
            results = []
            
            # 创建临时对话ID
            temp_conversation_id = f"test_{personality_id}"
            
            for i, user_input in enumerate(test_messages):
                print(f"\n👤 用户: {user_input}")
                
                # 生成AI回复
                ai_response = await self.simulator.generate_response(
                    personality_id=personality_id,
                    user_input=user_input,
                    db=None,  # 测试模式不需要保存
                    conversation_id=temp_conversation_id,
                    conversation_history=conversation_history
                )
                
                print(f"🤖 AI: {ai_response}")
                
                # 更新对话历史
                conversation_history.append(f"user: {user_input}")
                conversation_history.append(f"ai: {ai_response}")
                
                results.append({
                    "user": user_input,
                    "ai": ai_response
                })
            
            return results
            
        except Exception as e:
            logger.error("Failed to test character conversation", error=str(e))
            raise
    
    async def delete_character(self, personality_id: str) -> bool:
        """删除角色"""
        try:
            async with get_db_session() as db:
                # 删除相关数据
                await db.execute(
                    select(Entity).where(Entity.personality_id == personality_id)
                )
                await db.execute(
                    select(Belief).where(Belief.personality_id == personality_id)
                )
                await db.execute(
                    select(Event).where(Event.personality_id == personality_id)
                )
                await db.execute(
                    select(FamilyMember).where(FamilyMember.personality_id == personality_id)
                )
                
                # 删除主记录
                result = await db.execute(
                    select(PersonalityProfile).where(
                        PersonalityProfile.profile_id == personality_id
                    )
                )
                personality = result.scalar_one_or_none()
                
                if personality:
                    await db.delete(personality)
                    await db.commit()
                    return True
                
                return False
                
        except Exception as e:
            logger.error("Failed to delete character", error=str(e))
            raise
    
    def print_character_list(self, characters: List[Dict[str, Any]]):
        """打印角色列表"""
        if not characters:
            print("📭 暂无角色数据")
            return
        
        print(f"\n📋 角色列表 (共 {len(characters)} 个):")
        print("-" * 80)
        
        for i, char in enumerate(characters, 1):
            print(f"{i:2d}. {char['name']} ({char['id'][:8]}...)")
            print(f"     描述: {char['description'][:50]}...")
            print(f"     完成度: {char['completion']:.1f}%")
            print(f"     创建时间: {char['created_at']}")
            print(f"     大五人格: O={char['big_five']['openness']:.2f} "
                  f"C={char['big_five']['conscientiousness']:.2f} "
                  f"E={char['big_five']['extraversion']:.2f}")
            print()
    
    def print_character_details(self, details: Dict[str, Any]):
        """打印角色详细信息"""
        personality = details["personality"]
        
        print(f"\n🎭 角色详细信息: {personality.target_name}")
        print("=" * 60)
        
        print(f"ID: {personality.profile_id}")
        print(f"描述: {personality.description}")
        print(f"完成度: {personality.completion_percentage}%")
        print(f"创建时间: {personality.created_at}")
        
        print(f"\n📊 大五人格:")
        print(f"   开放性: {personality.openness_score:.2f}")
        print(f"   尽责性: {personality.conscientiousness_score:.2f}")
        print(f"   外向性: {personality.extraversion_score:.2f}")
        print(f"   宜人性: {personality.agreeableness_score:.2f}")
        print(f"   神经质: {personality.neuroticism_score:.2f}")
        
        print(f"\n🧠 认知特征:")
        print(f"   主导风格: {personality.dominant_cognitive_style}")
        print(f"   决策速度: {personality.decision_making_speed:.2f}")
        print(f"   风险承受: {personality.risk_tolerance:.2f}")
        
        print(f"\n💬 沟通风格:")
        print(f"   回复长度: {personality.average_response_length:.2f}")
        print(f"   词汇复杂度: {personality.vocabulary_complexity:.2f}")
        print(f"   情感表达: {personality.emotional_expressiveness:.2f}")
        
        print(f"\n👥 相关实体 ({len(details['entities'])} 个):")
        for entity in details["entities"][:5]:  # 只显示前5个
            print(f"   • {entity.name} ({entity.entity_type}) - 重要性: {entity.importance_score:.2f}")
        
        print(f"\n💭 核心信念 ({len(details['beliefs'])} 个):")
        for belief in details["beliefs"][:3]:  # 只显示前3个
            print(f"   • {belief.statement[:50]}... (强度: {belief.conviction_strength:.2f})")
        
        print(f"\n📅 重要事件 ({len(details['events'])} 个):")
        for event in details["events"][:3]:  # 只显示前3个
            print(f"   • {event.title} (年龄{event.age_at_event}) - 影响: {event.emotional_impact:.2f}")
        
        print(f"\n👨‍👩‍👧‍👦 家庭成员 ({len(details['family_members'])} 个):")
        for member in details["family_members"]:
            print(f"   • {member.name} ({member.relationship_type})")

async def main():
    """主函数"""
    print("🎭 角色管理器")
    print("=" * 40)
    
    manager = CharacterManager()
    
    while True:
        print("\n请选择操作:")
        print("1. 列出所有角色")
        print("2. 查看角色详情")
        print("3. 测试角色对话")
        print("4. 删除角色")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        try:
            if choice == "0":
                print("👋 再见!")
                break
                
            elif choice == "1":
                characters = await manager.list_all_characters()
                manager.print_character_list(characters)
                
            elif choice == "2":
                personality_id = input("请输入角色ID: ").strip()
                details = await manager.get_character_details(personality_id)
                manager.print_character_details(details)
                
            elif choice == "3":
                personality_id = input("请输入角色ID: ").strip()
                
                print("请输入测试消息 (输入空行结束):")
                test_messages = []
                while True:
                    msg = input("消息: ").strip()
                    if not msg:
                        break
                    test_messages.append(msg)
                
                if test_messages:
                    print(f"\n🧪 开始测试对话...")
                    results = await manager.test_character_conversation(
                        personality_id, test_messages
                    )
                    print(f"\n✅ 对话测试完成!")
                else:
                    print("❌ 没有输入测试消息")
                
            elif choice == "4":
                personality_id = input("请输入要删除的角色ID: ").strip()
                confirm = input(f"确认删除角色 {personality_id}? (y/N): ").strip().lower()
                
                if confirm == 'y':
                    success = await manager.delete_character(personality_id)
                    if success:
                        print("✅ 角色删除成功")
                    else:
                        print("❌ 角色不存在或删除失败")
                else:
                    print("❌ 取消删除")
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print(f"\n⚠️  操作中断")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
