<template>
  <div class="personality-detail" v-loading="loading">
    <div v-if="personality">
      <el-page-header @back="goBack" class="page-header">
        <template #content>
          <span class="text-large font-600 mr-3">{{ personality.target_name }}</span>
        </template>
      </el-page-header>

      <el-card class="description-card">
        <p>{{ personality.description || '暂无详细描述。' }}</p>
      </el-card>

      <el-row :gutter="20">
        <!-- 左侧：人格特质 -->
        <el-col :xs="24" :md="10">
          <el-card>
            <template #header>
              <h3>人格特质</h3>
            </template>
            <!-- 大五人格雷达图 -->
            <div ref="radarChart" style="width: 100%; height: 300px;"></div>
            <!-- 其他核心特质 -->
            <el-descriptions :column="1" border>
              <el-descriptions-item label="依恋类型">
                <el-tag>{{ personality.attachment_style || '未知' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文化背景">
                {{ formatCulturalBackground(personality.cultural_background) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- 右侧：人生故事 -->
        <el-col :xs="24" :md="14">
          <el-card>
            <template #header>
              <h3>人生故事</h3>
            </template>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="关键事件" name="events">
                <el-timeline v-if="personality.events.length > 0">
                  <el-timeline-item
                    v-for="(event, index) in personality.events"
                    :key="index"
                    :timestamp="`年龄: ${event.age}`"
                    placement="top"
                  >
                    <el-card>
                      <h4>{{ event.title }}</h4>
                      <p>{{ event.narrative }}</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
                <el-empty v-else description="暂无关键事件记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="核心信念" name="beliefs">
                <el-collapse v-if="personality.beliefs.length > 0">
                  <el-collapse-item
                    v-for="(belief, index) in personality.beliefs"
                    :key="index"
                    :title="belief.statement"
                  >
                    <div>{{ belief.explanation }}</div>
                  </el-collapse-item>
                </el-collapse>
                <el-empty v-else description="暂无核心信念记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="家庭关系" name="family">
                 <div v-if="personality.family_members.length > 0">
                    <div v-for="(member, index) in personality.family_members" :key="index" class="family-member">
                       <strong>{{ member.relationship }}:</strong>
                       <span>{{ JSON.stringify(member.summary) }}</span>
                    </div>
                 </div>
                <el-empty v-else description="暂无家庭关系记录"></el-empty>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-empty v-else-if="!loading" description="未找到该人格档案"></el-empty>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import api from '../../utils/api'; // 使用默认导出的axios实例

echarts.use([TitleComponent, TooltipComponent, LegendComponent, RadarChart, CanvasRenderer]);

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const personality = ref(null);
const activeTab = ref('events');
const radarChart = ref(null);
let myChart = null;

const goBack = () => {
  router.push('/personalities');
};

const formatCulturalBackground = (bg) => {
  if (!bg) return '未知';
  return `${bg.region || ''} ${bg.generation || ''} ${bg.ethnicity || ''}`.trim();
};

const setupRadarChart = () => {
  if (radarChart.value && personality.value) {
    myChart = echarts.init(radarChart.value);
    const bigFive = personality.value.big_five;
    const option = {
      tooltip: {},
      radar: {
        indicator: [
          { name: '开放性 (O)', max: 1 },
          { name: '尽责性 (C)', max: 1 },
          { name: '外向性 (E)', max: 1 },
          { name: '宜人性 (A)', max: 1 },
          { name: '神经质 (N)', max: 1 },
        ],
      },
      series: [
        {
          name: '大五人格',
          type: 'radar',
          data: [
            {
              value: [
                bigFive.openness,
                bigFive.conscientiousness,
                bigFive.extraversion,
                bigFive.agreeableness,
                bigFive.neuroticism,
              ],
              name: personality.value.target_name,
            },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }
};

onMounted(async () => {
  const personalityId = route.params.id;
  if (!personalityId) {
    ElMessage.error('无效的人格档案ID');
    loading.value = false;
    return;
  }
  try {
    const response = await api.get(`/personalities/${personalityId}`);
    personality.value = response.data;
    await nextTick();
    setupRadarChart();
  } catch (error) {
    console.error('加载人格详情失败:', error);
    ElMessage.error('加载详情失败');
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.personality-detail {
  padding: 20px;
}
.page-header {
  margin-bottom: 20px;
}
.description-card {
  margin-bottom: 20px;
}
.family-member {
  margin-bottom: 10px;
  font-size: 14px;
}
</style>
