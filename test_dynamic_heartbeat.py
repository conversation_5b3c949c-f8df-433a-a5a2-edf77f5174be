#!/usr/bin/env python3
"""
测试动态心跳功能的脚本
验证AI的情绪和关系状态是否能根据用户输入动态变化
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"

async def test_dynamic_heartbeat():
    """测试动态心跳功能"""
    
    async with aiohttp.ClientSession() as session:
        print("🧪 开始测试动态心跳功能...")
        
        # 1. 获取第一个人格档案
        async with session.get(f"{BASE_URL}/personalities") as resp:
            personalities = await resp.json()
            if not personalities:
                print("❌ 没有找到人格档案，请先创建一个")
                return
            
            personality_id = personalities[0]["profile_id"]
            personality_name = personalities[0]["target_name"]
            print(f"📋 使用人格档案: {personality_name} ({personality_id})")
        
        # 2. 启动对话
        start_data = {
            "personality_id": personality_id,
            "initial_message": "你好"
        }
        
        async with session.post(f"{BASE_URL}/simulation/start/{personality_id}", json=start_data) as resp:
            if resp.status != 200:
                print(f"❌ 启动对话失败: {resp.status}")
                return
            
            result = await resp.json()
            conversation_id = result["conversation_id"]
            print(f"✅ 对话启动成功: {conversation_id}")
            print(f"🤖 AI初始回复: {result['ai_response']}")
        
        # 3. 测试正面情绪输入
        print("\n🌟 测试正面情绪输入...")
        positive_messages = [
            "你太棒了！我很喜欢和你聊天",
            "感谢你的回复，真的很开心",
            "你说得太好了，我觉得你很赞"
        ]
        
        for msg in positive_messages:
            chat_data = {"user_input": msg}
            async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"👤 用户: {msg}")
                    print(f"🤖 AI: {result['ai_response']}")
                    print("---")
                else:
                    print(f"❌ 发送消息失败: {resp.status}")
        
        # 4. 测试负面情绪输入
        print("\n😔 测试负面情绪输入...")
        negative_messages = [
            "我觉得很失望，这样不好",
            "你说的话让我很生气",
            "这太糟糕了，我讨厌这样"
        ]
        
        for msg in negative_messages:
            chat_data = {"user_input": msg}
            async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"👤 用户: {msg}")
                    print(f"🤖 AI: {result['ai_response']}")
                    print("---")
                else:
                    print(f"❌ 发送消息失败: {resp.status}")
        
        # 5. 再次测试正面输入，看是否能恢复
        print("\n🌈 测试情绪恢复...")
        recovery_msg = "对不起刚才的话，你其实很好，我很感谢你"
        chat_data = {"user_input": recovery_msg}
        async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"👤 用户: {recovery_msg}")
                print(f"🤖 AI: {result['ai_response']}")
            else:
                print(f"❌ 发送消息失败: {resp.status}")
        
        print("\n✅ 动态心跳测试完成！")
        print("💡 观察AI的回复是否体现了情绪和关系的变化")

if __name__ == "__main__":
    asyncio.run(test_dynamic_heartbeat())
