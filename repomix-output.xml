This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: .history, *.md, frontend/build, backend/project_storage, docs/images, frontend/src/components/home/<USER>/src/lib/home.tsx, todo/, backend/logs
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
backend/.env
backend/.env.example
backend/app/__init__.py
backend/app/api/__init__.py
backend/app/api/endpoints/__init__.py
backend/app/api/endpoints/simulation.py
backend/app/database/__init__.py
backend/app/database/db_session.py
backend/app/database/models.py
backend/app/services/__init__.py
backend/app/services/personality_analyzer.py
backend/app/services/personality_simulator.py
backend/batch_character_generator.py
backend/character_generator.py
backend/character_manager.py
backend/init_db.py
backend/main.py
backend/requirements.txt
backend/sql/init.sql
docker-compose.yml
frontend/.env.example
frontend/index.html
frontend/package.json
frontend/src/App.vue
frontend/src/components/common/GlobalNotifications.vue
frontend/src/components/layout/AppHeader.vue
frontend/src/components/layout/AppSidebar.vue
frontend/src/main.js
frontend/src/router/index.js
frontend/src/stores/app.js
frontend/src/stores/auth.js
frontend/src/utils/api.js
frontend/src/views/auth/Login.vue
frontend/src/views/auth/Register.vue
frontend/src/views/chat/ChatInterface.vue
frontend/src/views/Dashboard.vue
frontend/src/views/error/NotFound.vue
frontend/src/views/personality/PersonalityCreate.vue
frontend/src/views/personality/PersonalityDetail.vue
frontend/src/views/personality/PersonalityList.vue
frontend/vite.config.js
LICENSE
start_optimized.py
test_fixes.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="backend/.env">
# API Keys
GEMINI_API_KEY="AIzaSyBV1VtK__aN7ZM1KNnkNXQ5GkAHOVbWk0A"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections
DATABASE_URL="postgresql+asyncpg://root:wufushen123@localhost:5432/personality_clone_db"
# 无Docker模式使用: DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"

# 暂时注释掉未使用的数据库配置
# NEO4J_URI="bolt://localhost:7687"
# NEO4J_USER="neo4j"
# NEO4J_PASSWORD="password"
# CHROMA_HOST="localhost"
# CHROMA_PORT="8001"
# REDIS_URL="redis://localhost:6500"
# ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
</file>

<file path="backend/.env.example">
# API Keys
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"
# 无Docker模式使用: DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"

# 暂时注释掉未使用的数据库配置
# NEO4J_URI="bolt://localhost:7687"
# NEO4J_USER="neo4j"
# NEO4J_PASSWORD="password"
# CHROMA_HOST="localhost"
# CHROMA_PORT="8001"
# REDIS_URL="redis://localhost:6500"
# ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
EMOTION_MODEL="j-hartmann/emotion-english-distilroberta-base"
</file>

<file path="backend/app/__init__.py">
# 人格复刻系统后端应用包
</file>

<file path="backend/app/api/__init__.py">
# API package
</file>

<file path="backend/app/api/endpoints/__init__.py">
# API endpoints package
</file>

<file path="backend/app/api/endpoints/simulation.py">
"""
Simulation API endpoints - 西牟拉胡协议的API接口
提供AI角色扮演和人格模拟功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
from typing import List, Optional

from app.database.db_session import get_db_session
from app.database.models import User, PersonalityProfile, Conversation, Message
from app.services.personality_simulator import PersonalitySimulator
import structlog

logger = structlog.get_logger()

router = APIRouter()
simulator = PersonalitySimulator()

# === Pydantic Models ===

class SimulationRequest(BaseModel):
    user_input: str
    conversation_id: Optional[str] = None

class SimulationResponse(BaseModel):
    ai_response: str
    conversation_id: str
    personality_name: str
    response_metadata: Optional[dict] = None

class StartSimulationRequest(BaseModel):
    personality_id: str
    initial_message: Optional[str] = "你好"

# === Helper Functions ===

async def get_current_user_simple(db: AsyncSession) -> User:
    """简化的用户获取（用于测试）"""
    # 这里应该实现真正的用户认证
    # 暂时返回第一个用户用于测试
    result = await db.execute(select(User).limit(1))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No user found. Please register first."
        )
    return user

# === API Endpoints ===

@router.post("/simulation/start/{personality_id}", response_model=SimulationResponse)
async def start_personality_simulation(
    personality_id: str,
    request: StartSimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    启动与指定人格的模拟对话
    这是西牟拉胡协议的入口点
    """
    try:
        # 获取用户（简化版本）
        user = await get_current_user_simple(db)
        
        # 验证人格档案是否存在
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 创建新的对话会话
        conversation = Conversation(
            user_id=user.user_id,
            personality_id=personality.profile_id
        )
        
        db.add(conversation)
        await db.commit()
        await db.refresh(conversation)
        
        # 生成AI的初始回复
        ai_response = await simulator.generate_response(
            personality_id=personality_id,
            user_input=request.initial_message,
            db=db,
            conversation_id=str(conversation.conversation_id), # 新增这一行
            conversation_history=[]
        )
        
        # 保存初始消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.initial_message
        )
        
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(user_message)
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Started personality simulation",
            personality_id=personality_id,
            conversation_id=str(conversation.conversation_id),
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=str(conversation.conversation_id),
            personality_name=personality.target_name,
            response_metadata={
                "completion_percentage": personality.completion_percentage,
                "attachment_style": personality.attachment_style
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start simulation"
        )

@router.post("/simulation/chat/{conversation_id}", response_model=SimulationResponse)
async def continue_personality_simulation(
    conversation_id: str,
    request: SimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    继续与人格的模拟对话
    """
    try:
        # 获取对话会话
        result = await db.execute(
            select(Conversation).where(Conversation.conversation_id == conversation_id)
        )
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # 获取人格档案
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == conversation.personality_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 获取对话历史
        history_result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation.conversation_id)
            .order_by(Message.timestamp)
        )
        messages = history_result.scalars().all()
        conversation_history = [f"{msg.sender}: {msg.content}" for msg in messages]
        
        # 保存用户消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.user_input
        )
        
        db.add(user_message)
        await db.commit()
        
        # 生成AI回复
        ai_response = await simulator.generate_response(
            personality_id=str(conversation.personality_id),
            user_input=request.user_input,
            db=db,
            conversation_id=conversation_id, # 新增这一行
            conversation_history=conversation_history
        )
        
        # 保存AI回复
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Continued personality simulation",
            conversation_id=conversation_id,
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=conversation_id,
            personality_name=personality.target_name,
            response_metadata={
                "message_count": len(conversation_history) + 2,
                "completion_percentage": personality.completion_percentage
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to continue personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to continue simulation"
        )

@router.get("/simulation/conversations/{personality_id}")
async def get_personality_conversations(
    personality_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定人格的所有对话会话"""
    try:
        result = await db.execute(
            select(Conversation).where(Conversation.personality_id == personality_id)
        )
        conversations = result.scalars().all()
        
        return [
            {
                "conversation_id": str(conv.conversation_id),
                "started_at": conv.started_at.isoformat() if conv.started_at else None,
                "ended_at": conv.ended_at.isoformat() if conv.ended_at else None
            }
            for conv in conversations
        ]
        
    except Exception as e:
        logger.error("Failed to get personality conversations", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversations"
        )

@router.get("/simulation/messages/{conversation_id}")
async def get_conversation_messages(
    conversation_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定对话的所有消息"""
    try:
        result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .order_by(Message.timestamp)
        )
        messages = result.scalars().all()
        
        return [
            {
                "message_id": str(msg.message_id),
                "sender": msg.sender,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None
            }
            for msg in messages
        ]
        
    except Exception as e:
        logger.error("Failed to get conversation messages", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get messages"
        )
</file>

<file path="backend/app/database/__init__.py">
# 数据库模块
</file>

<file path="backend/app/database/db_session.py">
"""
Database session management with connection pooling and error handling
"""

import os
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import event
from dotenv import load_dotenv
import structlog

from .models import Base

load_dotenv()

logger = structlog.get_logger()

class DatabaseManager:
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Create async engine with optimized settings
        self.engine = create_async_engine(
            self.database_url,
            echo=os.getenv("DEBUG", "false").lower() == "true",
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
            connect_args={
                "server_settings": {
                    "application_name": "personality_clone_backend",
                }
            }
        )
        
        # Create session factory
        self.async_session_factory = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Add connection event listeners
        self._setup_event_listeners()
    
    def _setup_event_listeners(self):
        """Setup database event listeners for monitoring"""
        
        @event.listens_for(self.engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            # This is for PostgreSQL, but we can add connection optimizations here
            pass
        
        @event.listens_for(self.engine.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            logger.debug("Database connection checked out")
        
        @event.listens_for(self.engine.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            logger.debug("Database connection checked in")

    async def create_tables(self):
        """Create all database tables"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with proper error handling"""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", error=str(e))
                raise
            finally:
                await session.close()

    async def health_check(self) -> bool:
        """Check database connectivity"""
        try:
            async with self.async_session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False

    async def close(self):
        """Close database connections"""
        await self.engine.dispose()
        logger.info("Database connections closed")

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database sessions"""
    async for session in db_manager.get_session():
        yield session

# Utility functions for database operations
async def execute_with_retry(session: AsyncSession, operation, max_retries: int = 3):
    """Execute database operation with retry logic"""
    for attempt in range(max_retries):
        try:
            result = await operation(session)
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            if attempt == max_retries - 1:
                logger.error(
                    "Database operation failed after retries",
                    error=str(e),
                    attempts=max_retries
                )
                raise
            else:
                logger.warning(
                    "Database operation failed, retrying",
                    error=str(e),
                    attempt=attempt + 1
                )
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

class TransactionManager:
    """Context manager for database transactions across multiple operations"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.savepoint = None
    
    async def __aenter__(self):
        self.savepoint = await self.session.begin_nested()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.savepoint.rollback()
            logger.error("Transaction rolled back", error=str(exc_val))
        else:
            await self.savepoint.commit()
            logger.debug("Transaction committed successfully")

# Database initialization function
async def init_database():
    """Initialize database with tables and basic data"""
    try:
        await db_manager.create_tables()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise

# Cleanup function
async def cleanup_database():
    """Cleanup database connections"""
    await db_manager.close()
</file>

<file path="backend/app/database/models.py">
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Float, SMALLINT, ForeignKey, Enum, JSON, DateTime, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class PersonalityDimension(enum.Enum):
    OPENNESS = "openness"
    CONSCIENTIOUSNESS = "conscientiousness"
    EXTRAVERSION = "extraversion"
    AGREEABLENESS = "agreeableness"
    NEUROTICISM = "neuroticism"

class CognitiveStyle(enum.Enum):
    ANALYTICAL = "analytical"
    INTUITIVE = "intuitive"
    SYSTEMATIC = "systematic"
    CREATIVE = "creative"

class EmotionalState(enum.Enum):
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    NEUTRAL = "neutral"

class User(Base):
    __tablename__ = 'users'
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    personalities = relationship("PersonalityProfile", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")

class PersonalityProfile(Base):
    __tablename__ = 'personality_profiles'
    profile_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    target_name = Column(String(255), nullable=False)  # 被复刻人的名字
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completion_percentage = Column(Float, default=0.0)  # 复刻完成度
    
    # Big Five personality scores
    openness_score = Column(Float, default=0.5)
    conscientiousness_score = Column(Float, default=0.5)
    extraversion_score = Column(Float, default=0.5)
    agreeableness_score = Column(Float, default=0.5)
    neuroticism_score = Column(Float, default=0.5)
    
    # Cognitive patterns
    dominant_cognitive_style = Column(Enum(CognitiveStyle))
    decision_making_speed = Column(Float)  # 0-1, slow to fast
    risk_tolerance = Column(Float)  # 0-1, risk-averse to risk-seeking
    
    # Communication patterns
    average_response_length = Column(Float)
    vocabulary_complexity = Column(Float)  # 0-1, simple to complex
    emotional_expressiveness = Column(Float)  # 0-1, reserved to expressive

    # 身份同心圆核心数据
    attachment_style = Column(String(50), default='secure')  # 'secure', 'anxious', 'avoidant', 'disorganized'
    cultural_background = Column(JSON, nullable=True)  # {"ethnicity": "汉族", "region": "华北", "generation": "90后"}

    user = relationship("User", back_populates="personalities")
    entities = relationship("Entity", back_populates="personality")
    beliefs = relationship("Belief", back_populates="personality")
    events = relationship("Event", back_populates="personality")
    conversations = relationship("Conversation", back_populates="personality")
    family_members = relationship("FamilyMember", back_populates="personality")

class Entity(Base):
    __tablename__ = 'entities'
    entity_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    name = Column(String(255), nullable=False, index=True)
    entity_type = Column(String(50), nullable=False)  # person, place, concept, etc.
    relationship_type = Column(String(100))  # family, friend, colleague, etc.
    emotional_valence = Column(Float)  # -1 to 1, negative to positive
    importance_score = Column(Float)  # 0-1
    profile = Column(JSON, nullable=True)

    personality = relationship("PersonalityProfile", back_populates="entities")

class Belief(Base):
    __tablename__ = 'beliefs'
    belief_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    statement = Column(Text, nullable=False)
    belief_category = Column(String(50), nullable=False)  # moral, political, personal, etc.
    conviction_strength = Column(Float, nullable=False)  # 0-1
    flexibility_score = Column(Float)  # 0-1, rigid to flexible
    origin_context = Column(Text)  # How this belief was formed
    full_explanation = Column(Text)
    
    personality = relationship("PersonalityProfile", back_populates="beliefs")

class Event(Base):
    __tablename__ = 'events'
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    title = Column(String(255), nullable=False)
    age_at_event = Column(SMALLINT)
    life_stage = Column(String(50))  # childhood, adolescence, young_adult, etc.
    event_type = Column(String(50))  # achievement, trauma, relationship, etc.
    emotional_impact = Column(Float)  # -1 to 1
    centrality_score = Column(Float, nullable=False)  # 0-1
    memory_vividness = Column(Float)  # 0-1
    lessons_learned = Column(ARRAY(String))
    full_narrative = Column(Text)
    
    personality = relationship("PersonalityProfile", back_populates="events")

class CognitivePattern(Base):
    __tablename__ = 'cognitive_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50), nullable=False)  # decision_tree, reaction_pattern, etc.
    trigger_conditions = Column(JSON)  # What triggers this pattern
    typical_response = Column(Text)
    confidence_level = Column(Float)  # 0-1
    frequency_observed = Column(Integer, default=1)
    context_tags = Column(ARRAY(String))

class LanguagePattern(Base):
    __tablename__ = 'language_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50))  # vocabulary, syntax, style, etc.
    pattern_data = Column(JSON)  # Specific pattern details
    frequency = Column(Float)  # How often this pattern appears
    context = Column(String(100))  # formal, casual, emotional, etc.

class EmotionalResponse(Base):
    __tablename__ = 'emotional_responses'
    response_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    trigger_situation = Column(Text, nullable=False)
    primary_emotion = Column(Enum(EmotionalState), nullable=False)
    intensity = Column(Float)  # 0-1
    duration_pattern = Column(String(50))  # quick, moderate, prolonged
    coping_mechanism = Column(Text)
    typical_expression = Column(Text)  # How they express this emotion

class Conversation(Base):
    __tablename__ = 'conversations'
    conversation_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    session_data = Column(JSON)  # Store session state
    
    user = relationship("User", back_populates="conversations")
    personality = relationship("PersonalityProfile", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation")

class FamilyMember(Base):
    __tablename__ = 'family_members'
    member_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    relationship_type = Column(String(50), nullable=False)  # "父亲", "母亲", "兄长"
    name = Column(String(255))
    personality_summary = Column(JSON)  # 简化的人格模型, {"extraversion": 0.8, "agreeableness": 0.3}
    parenting_style = Column(String(50))  # "权威型", "专断型" 等, 仅父母有

    personality = relationship("PersonalityProfile", back_populates="family_members")

class Message(Base):
    __tablename__ = 'messages'
    message_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('conversations.conversation_id'), nullable=False)
    sender = Column(String(20), nullable=False)  # 'user' or 'ai'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    analysis_data = Column(JSON)  # Store analysis results

    conversation = relationship("Conversation", back_populates="messages")
</file>

<file path="backend/app/services/__init__.py">
# 服务模块
</file>

<file path="backend/app/services/personality_analyzer.py">
"""
简化的人格分析服务
仅保留AI角色对话所需的基本功能
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from google import genai
from google.genai import types
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from dotenv import load_dotenv

from app.database.models import PersonalityProfile

load_dotenv()

class PersonalityAnalyzer:
    """简化的人格分析器，仅用于支持AI角色对话"""

    def __init__(self):
        # Initialize Google GenAI client (new SDK)
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False

    async def get_personality_completion_status(
        self,
        personality_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """计算人格档案完成状态（仅用于显示）"""

        # Query database for current personality data
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()

        if not personality:
            return {"completion_percentage": 0.0, "missing_areas": []}

        # Calculate completion based on available data
        completion_factors = {
            "basic_info": 1.0 if personality.target_name else 0.0,
            "big_five": sum([
                1.0 if personality.openness_score != 0.5 else 0.0,
                1.0 if personality.conscientiousness_score != 0.5 else 0.0,
                1.0 if personality.extraversion_score != 0.5 else 0.0,
                1.0 if personality.agreeableness_score != 0.5 else 0.0,
                1.0 if personality.neuroticism_score != 0.5 else 0.0,
            ]) / 5.0,
            "cognitive_style": 1.0 if personality.dominant_cognitive_style else 0.0,
            "communication": 1.0 if personality.average_response_length else 0.0,
        }

        overall_completion = sum(completion_factors.values()) / len(completion_factors) * 100

        missing_areas = [
            area for area, score in completion_factors.items() if score < 0.8
        ]

        return {
            "completion_percentage": overall_completion,
            "missing_areas": missing_areas,
            "completion_factors": completion_factors
        }
</file>

<file path="backend/app/services/personality_simulator.py">
"""
PersonalitySimulator - 西牟拉胡协议的核心实现
负责AI角色扮演和人格模拟
"""

import json
import os
import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from google import genai
from google.genai import types
from dotenv import load_dotenv

from app.database.models import (
    PersonalityProfile, Entity, Belief, Event,
    FamilyMember, CognitivePattern, EmotionalResponse, Conversation
)
import structlog

# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

# Genesis Prompt模板 - 用于AI角色扮演
SIMULATION_GENESIS_PROMPT = """
你现在要完全代入{target_name}这个人的身份，以第一人称的方式与用户对话。

## 人格特征
- 开放性: {openness:.2f} (0=保守传统, 1=开放创新)
- 尽责性: {conscientiousness:.2f} (0=随性自由, 1=严谨负责)
- 外向性: {extraversion:.2f} (0=内向安静, 1=外向活跃)
- 宜人性: {agreeableness:.2f} (0=竞争独立, 1=合作友善)
- 神经质: {neuroticism:.2f} (0=情绪稳定, 1=情绪敏感)

## 身份背景
- 依恋风格: {attachment_style}
- 文化背景: {cultural_background}

## 核心信念
{core_beliefs}

## 重要人际关系
{important_relationships}

## 关键人生事件
{key_life_events}

## 当前状态
- 情绪状态: {current_mood}
- 精力水平: {energy}/100
- 亲密度: {intimacy}/100
- 信任度: {trust}/100

## 相关记忆
{retrieved_memories}

## 对话历史
{conversation_history}

## 用户刚才说
{user_input}

请以{target_name}的身份，根据以上所有信息，自然地回复用户。回复要：
1. 完全符合{target_name}的人格特征和说话风格
2. 体现当前的情绪状态和关系状态
3. 适当引用相关的记忆和经历
4. 保持对话的连贯性和真实感
5. 回复长度适中，不要过长或过短

回复：
"""

class PersonalitySimulator:
    """AI人格模拟器 - 让AI完全代入目标人物身份"""

    def __init__(self):
        # 初始化最新的 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            # 使用新的统一SDK
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False
        logger.info(f"PersonalitySimulator initialized. LLM available: {self.available}")
    
    async def generate_response(
        self,
        personality_id: str,
        user_input: str,
        db: AsyncSession,
        conversation_id: str, # 新增 conversation_id
        conversation_history: List[str] = None
    ) -> str:
        """
        生成目标人物的回复
        这是西牟拉胡协议的核心方法
        """
        try:
            # 1. 从数据库加载完整的人格档案
            profile_data = await self._load_full_profile(personality_id, db)
            
            if not profile_data:
                return "抱歉，无法找到对应的人格档案。"
            
            # 新增: 加载 conversation 对象
            conv_result = await db.execute(select(Conversation).where(Conversation.conversation_id == conversation_id))
            conversation = conv_result.scalar_one_or_none()
            if not conversation:
                raise ValueError("Conversation not found for dynamic state update.")

            # 2. 构建动态状态
            dynamic_state = await self._calculate_dynamic_state(profile_data, user_input, conversation, db) # 传递 conversation
            
            # 3. 检索相关记忆（暂时使用简化版本）
            retrieved_memories = await self._retrieve_relevant_memories(
                profile_data, user_input, db
            )
            
            # 4. 组装Genesis Prompt
            prompt = await self._build_genesis_prompt(
                profile_data, dynamic_state, retrieved_memories, 
                user_input, conversation_history or []
            )
            
            # 5. 调用LLM生成回复
            response = await self._call_llm(prompt)
            
            logger.info(
                "Generated personality response",
                personality_id=personality_id,
                target_name=profile_data['target_name']
            )
            
            return response
            
        except Exception as e:
            logger.error("Failed to generate personality response", error=str(e))
            return f"作为{profile_data.get('target_name', '未知')}，我现在有些困惑，请稍后再试。"
    
    async def _load_full_profile(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """从数据库加载完整的人格档案数据"""
        try:
            # 加载基础人格档案
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                return None
            
            # 加载相关数据
            beliefs = await self._load_beliefs(personality_id, db)
            entities = await self._load_entities(personality_id, db)
            events = await self._load_events(personality_id, db)
            family_members = await self._load_family_members(personality_id, db)
            
            return {
                'target_name': profile.target_name,
                'description': profile.description,
                'big_five': {
                    'openness': profile.openness_score or 0.5,
                    'conscientiousness': profile.conscientiousness_score or 0.5,
                    'extraversion': profile.extraversion_score or 0.5,
                    'agreeableness': profile.agreeableness_score or 0.5,
                    'neuroticism': profile.neuroticism_score or 0.5
                },
                'attachment_style': profile.attachment_style or 'secure',
                'cultural_background': profile.cultural_background or {},
                'beliefs': beliefs,
                'entities': entities,
                'events': events,
                'family_members': family_members,
                'communication_style': {
                    'response_length': profile.average_response_length or 0.5,
                    'vocabulary_complexity': profile.vocabulary_complexity or 0.5,
                    'emotional_expressiveness': profile.emotional_expressiveness or 0.5
                }
            }
            
        except Exception as e:
            logger.error("Failed to load full profile", error=str(e))
            return None
    
    async def _load_beliefs(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载信念数据"""
        result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        beliefs = result.scalars().all()
        return [
            {
                'statement': belief.statement,
                'category': belief.belief_category,
                'conviction': belief.conviction_strength,
                'explanation': belief.full_explanation
            }
            for belief in beliefs
        ]
    
    async def _load_entities(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载重要人物/实体数据"""
        result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        entities = result.scalars().all()
        return [
            {
                'name': entity.name,
                'type': entity.entity_type,
                'relationship': entity.relationship_type,
                'emotional_valence': entity.emotional_valence,
                'importance': entity.importance_score
            }
            for entity in entities
        ]
    
    async def _load_events(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载关键人生事件"""
        result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        events = result.scalars().all()
        return [
            {
                'title': event.title,
                'age': event.age_at_event,
                'type': event.event_type,
                'emotional_impact': event.emotional_impact,
                'narrative': event.full_narrative
            }
            for event in events
        ]
    
    async def _load_family_members(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载家庭成员信息"""
        result = await db.execute(
            select(FamilyMember).where(FamilyMember.personality_id == personality_id)
        )
        family_members = result.scalars().all()
        return [
            {
                'relationship': member.relationship_type,
                'name': member.name,
                'personality_summary': member.personality_summary,
                'parenting_style': member.parenting_style
            }
            for member in family_members
        ]
    
    async def _calculate_dynamic_state(
        self,
        profile_data: Dict,
        user_input: str,
        conversation: Conversation,
        db: AsyncSession
    ) -> Dict:
        """计算并更新当前的动态状态（情绪、关系等）"""

        # 安全地获取 session_data，如果为 None 则初始化为空字典
        session_data = conversation.session_data or {}
        dynamic_state = {
            'current_mood': session_data.get('mood', '平静'),
            'energy': session_data.get('energy', 80),
            'intimacy': session_data.get('intimacy', 50),
            'trust': session_data.get('trust', 60)
        }

        # 2. 基于用户输入进行简单的情绪和关系调整 (这是一个可以无限深化的点)
        # 例如:
        positive_words = ["喜欢", "开心", "太棒了", "感谢", "爱", "好", "棒", "赞", "谢谢"]
        negative_words = ["讨厌", "失望", "糟糕", "恨", "坏", "烦", "差", "不好", "生气"]

        if any(word in user_input for word in positive_words):
            dynamic_state['intimacy'] = min(100, dynamic_state['intimacy'] + 2)
            dynamic_state['trust'] = min(100, dynamic_state['trust'] + 1)
            dynamic_state['current_mood'] = '开心'
            dynamic_state['energy'] = min(100, dynamic_state['energy'] + 5)
        elif any(word in user_input for word in negative_words):
            dynamic_state['intimacy'] = max(0, dynamic_state['intimacy'] - 3)
            dynamic_state['trust'] = max(0, dynamic_state['trust'] - 2)
            dynamic_state['current_mood'] = '低落'
            dynamic_state['energy'] = max(20, dynamic_state['energy'] - 10)

        # 3. 将更新后的状态写回数据库
        conversation.session_data = dynamic_state
        db.add(conversation)
        await db.flush() # 确保在提交前更新

        return dynamic_state
    
    async def _retrieve_relevant_memories(
        self, 
        profile_data: Dict, 
        user_input: str, 
        db: AsyncSession
    ) -> str:
        """检索与当前对话相关的记忆（简化版本）"""
        # 这里应该实现RAG检索，暂时返回最重要的几个记忆
        memories = []
        
        # 添加重要事件
        for event in profile_data.get('events', [])[:3]:
            if event.get('narrative'):
                memories.append(f"重要经历：{event['narrative']}")
        
        # 添加核心信念
        for belief in profile_data.get('beliefs', [])[:2]:
            if belief.get('statement'):
                memories.append(f"核心信念：{belief['statement']}")
        
        return '\n'.join(memories) if memories else "暂无相关记忆。"
    
    async def _build_genesis_prompt(
        self,
        profile_data: Dict,
        dynamic_state: Dict,
        retrieved_memories: str,
        user_input: str,
        conversation_history: List[str]
    ) -> str:
        """构建Genesis Prompt"""
        
        # 格式化核心信念
        core_beliefs = '\n'.join([
            f"- {belief['statement']}" 
            for belief in profile_data.get('beliefs', [])
        ]) or "暂无明确的核心信念记录。"
        
        # 格式化重要关系
        important_relationships = '\n'.join([
            f"- {entity['name']} ({entity['relationship']}): {entity.get('type', '重要人物')}"
            for entity in profile_data.get('entities', [])
        ]) or "暂无重要人际关系记录。"
        
        # 格式化关键事件
        key_life_events = '\n'.join([
            f"- {event['title']} (年龄{event['age']}): {event.get('narrative', '重要经历')}"
            for event in profile_data.get('events', [])
        ]) or "暂无关键人生事件记录。"
        
        # 格式化对话历史
        conversation_context = '\n'.join(conversation_history[-5:]) if conversation_history else "这是我们的第一次对话。"
        
        return SIMULATION_GENESIS_PROMPT.format(
            target_name=profile_data['target_name'],
            openness=profile_data['big_five']['openness'],
            conscientiousness=profile_data['big_five']['conscientiousness'],
            extraversion=profile_data['big_five']['extraversion'],
            agreeableness=profile_data['big_five']['agreeableness'],
            neuroticism=profile_data['big_five']['neuroticism'],
            attachment_style=profile_data['attachment_style'],
            cultural_background=json.dumps(profile_data['cultural_background'], ensure_ascii=False),
            core_beliefs=core_beliefs,
            important_relationships=important_relationships,
            key_life_events=key_life_events,
            current_mood=dynamic_state['current_mood'],
            energy=dynamic_state['energy'],
            intimacy=dynamic_state['intimacy'],
            trust=dynamic_state['trust'],
            retrieved_memories=retrieved_memories,
            conversation_history=conversation_context,
            user_input=user_input
        )
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM生成回复 (使用最新的Google GenAI SDK)"""
        if not self.available:
            logger.warning("LLM client not available, returning fallback response.")
            return "我理解你的话，但我的思考核心暂时无法连接。请检查API Key配置。（测试模式）"

        try:
            # 使用最新的统一SDK异步方法
            response = await self.client.aio.models.generate_content(
                model='gemini-2.0-flash-001',
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=0.75,
                    max_output_tokens=500,
                )
            )
            return response.text

        except Exception as e:
            logger.error("LLM call failed in simulator", error=str(e))
            return "我脑子里现在有点乱，好像想说些什么，但又说不出来...我们能等会儿再说这个吗？"
</file>

<file path="backend/batch_character_generator.py">
#!/usr/bin/env python3
"""
批量角色生成脚本
一次性生成多个不同类型的角色，用于快速填充数据库
"""

import asyncio
import sys
import os
from typing import List, Dict, Any
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from character_generator import CharacterGenerator
from app.database.db_session import init_database, get_db_session
from app.database.models import User
from sqlalchemy.future import select
import structlog

logger = structlog.get_logger()

# 预定义的角色类型和背景
CHARACTER_TEMPLATES = [
    {
        "type": "科技公司程序员",
        "context": "25-30岁，在一线城市工作，热爱编程和技术，有一定的社交焦虑，但在专业领域很自信。喜欢游戏、动漫，追求工作与生活的平衡。"
    },
    {
        "type": "文艺青年",
        "context": "22-28岁，喜欢文学、电影、音乐，有艺术追求，情感丰富，思想深刻。可能从事创意相关工作，对物质要求不高，更注重精神追求。"
    },
    {
        "type": "职场白领",
        "context": "28-35岁，在大公司工作，有一定的职业规划和野心，注重形象和人际关系，生活节奏较快，压力较大。"
    },
    {
        "type": "大学生",
        "context": "18-22岁，正在接受高等教育，对未来充满憧憬和不确定性，活跃在社交媒体，关注流行文化，有理想主义倾向。"
    },
    {
        "type": "创业者",
        "context": "25-40岁，有创业经历或正在创业，风险承受能力强，目标导向，善于沟通和说服他人，工作狂倾向。"
    },
    {
        "type": "教师",
        "context": "25-45岁，从事教育工作，有耐心和责任心，关心他人成长，知识面广，有一定的理想主义色彩。"
    },
    {
        "type": "医护人员",
        "context": "25-40岁，从事医疗相关工作，有强烈的责任感和同理心，工作压力大，但有使命感，关注健康和生命。"
    },
    {
        "type": "艺术家",
        "context": "20-50岁，从事艺术创作，个性鲜明，情感表达丰富，对美有独特见解，可能经济状况不稳定但精神富足。"
    },
    {
        "type": "退休老人",
        "context": "60-75岁，已退休，有丰富的人生阅历，关注健康和家庭，可能有一些传统观念，但也在适应新时代。"
    },
    {
        "type": "家庭主妇",
        "context": "25-45岁，以家庭为重心，有育儿经验，善于管理家务，可能有一些个人兴趣爱好，关注家人健康和教育。"
    }
]

class BatchCharacterGenerator:
    """批量角色生成器"""
    
    def __init__(self):
        self.generator = CharacterGenerator()
        self.generated_characters = []
    
    async def ensure_demo_user_exists(self) -> str:
        """确保演示用户存在"""
        try:
            async with get_db_session() as db:
                # 查找演示用户
                result = await db.execute(
                    select(User).where(User.username == "demo")
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    # 创建演示用户
                    user = User(
                        username="demo",
                        email="<EMAIL>",
                        hashed_password="demo_password_hash"  # 在实际应用中应该是真正的哈希
                    )
                    db.add(user)
                    await db.commit()
                    await db.refresh(user)
                    logger.info("Created demo user", user_id=str(user.user_id))
                
                return str(user.user_id)
                
        except Exception as e:
            logger.error("Failed to ensure demo user exists", error=str(e))
            raise
    
    async def generate_character_batch(
        self, 
        templates: List[Dict[str, str]] = None,
        user_id: str = None
    ) -> List[Dict[str, Any]]:
        """批量生成角色"""
        if templates is None:
            templates = CHARACTER_TEMPLATES
        
        if user_id is None:
            user_id = await self.ensure_demo_user_exists()
        
        results = []
        
        for i, template in enumerate(templates, 1):
            try:
                print(f"\n🎭 正在生成角色 {i}/{len(templates)}: {template['type']}")
                
                # 生成角色
                character = await self.generator.generate_character(
                    character_type=template['type'],
                    additional_context=template['context']
                )
                
                # 保存到数据库
                personality_id = await self.generator.save_character_to_db(character, user_id)
                
                result = {
                    "template": template,
                    "character": character,
                    "personality_id": personality_id,
                    "status": "success"
                }
                
                results.append(result)
                self.generated_characters.append(result)
                
                print(f"✅ 成功生成: {character.target_name} (ID: {personality_id})")
                
                # 添加延迟以避免API限制
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(
                    "Failed to generate character",
                    template_type=template['type'],
                    error=str(e)
                )
                
                result = {
                    "template": template,
                    "character": None,
                    "personality_id": None,
                    "status": "failed",
                    "error": str(e)
                }
                
                results.append(result)
                print(f"❌ 生成失败: {template['type']} - {e}")
        
        return results
    
    def print_summary(self, results: List[Dict[str, Any]]):
        """打印生成摘要"""
        successful = [r for r in results if r["status"] == "success"]
        failed = [r for r in results if r["status"] == "failed"]
        
        print(f"\n{'='*60}")
        print(f"📊 批量生成完成摘要")
        print(f"{'='*60}")
        print(f"总计: {len(results)} 个角色")
        print(f"成功: {len(successful)} 个")
        print(f"失败: {len(failed)} 个")
        
        if successful:
            print(f"\n✅ 成功生成的角色:")
            for result in successful:
                char = result["character"]
                print(f"   • {char.target_name} ({char.occupation}) - ID: {result['personality_id']}")
        
        if failed:
            print(f"\n❌ 生成失败的角色:")
            for result in failed:
                print(f"   • {result['template']['type']} - 错误: {result['error']}")
    
    async def generate_custom_characters(self, custom_templates: List[Dict[str, str]]):
        """生成自定义角色列表"""
        print(f"🎯 开始生成 {len(custom_templates)} 个自定义角色...")
        
        user_id = await self.ensure_demo_user_exists()
        results = await self.generate_character_batch(custom_templates, user_id)
        
        self.print_summary(results)
        return results

async def main():
    """主函数"""
    print("🎭 批量角色数据生成器")
    print("=" * 60)
    
    # 初始化数据库
    await init_database()
    
    batch_generator = BatchCharacterGenerator()
    
    print("请选择生成模式:")
    print("1. 生成所有预定义角色类型")
    print("2. 生成指定数量的随机角色")
    print("3. 自定义角色类型")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    try:
        if choice == "1":
            # 生成所有预定义角色
            print(f"\n🚀 开始生成 {len(CHARACTER_TEMPLATES)} 个预定义角色...")
            results = await batch_generator.generate_character_batch()
            batch_generator.print_summary(results)
            
        elif choice == "2":
            # 生成指定数量的随机角色
            count = int(input("请输入要生成的角色数量: "))
            if count > len(CHARACTER_TEMPLATES):
                print(f"⚠️  最多只能生成 {len(CHARACTER_TEMPLATES)} 个角色")
                count = len(CHARACTER_TEMPLATES)
            
            selected_templates = CHARACTER_TEMPLATES[:count]
            print(f"\n🚀 开始生成 {count} 个随机角色...")
            results = await batch_generator.generate_character_batch(selected_templates)
            batch_generator.print_summary(results)
            
        elif choice == "3":
            # 自定义角色类型
            custom_templates = []
            print("\n请输入自定义角色信息 (输入空行结束):")
            
            while True:
                char_type = input("角色类型: ").strip()
                if not char_type:
                    break
                
                context = input("背景描述: ").strip()
                custom_templates.append({
                    "type": char_type,
                    "context": context
                })
                print("✅ 已添加角色类型\n")
            
            if custom_templates:
                results = await batch_generator.generate_custom_characters(custom_templates)
            else:
                print("❌ 没有输入任何角色类型")
                return 1
        
        else:
            print("❌ 无效选择")
            return 1
        
        print(f"\n🎉 批量生成完成!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
</file>

<file path="backend/character_generator.py">
#!/usr/bin/env python3
"""
自动化角色数据生成脚本
使用最新的Google GenAI SDK生成完整的角色人格数据并写入数据库
"""

import asyncio
import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import google.generativeai as genai
import instructor
from app.database.db_session import get_db_session, init_database
from app.database.models import (
    PersonalityProfile, Entity, Belief, Event, CognitivePattern, 
    LanguagePattern, EmotionalResponse, FamilyMember, User,
    CognitiveStyle, EmotionalState
)
import structlog

# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

# === Pydantic 数据模型 ===

class BigFivePersonality(BaseModel):
    """大五人格模型"""
    openness: float = Field(..., ge=0.0, le=1.0, description="开放性")
    conscientiousness: float = Field(..., ge=0.0, le=1.0, description="尽责性")
    extraversion: float = Field(..., ge=0.0, le=1.0, description="外向性")
    agreeableness: float = Field(..., ge=0.0, le=1.0, description="宜人性")
    neuroticism: float = Field(..., ge=0.0, le=1.0, description="神经质")

class CommunicationStyle(BaseModel):
    """沟通风格"""
    average_response_length: float = Field(..., ge=0.1, le=5.0, description="平均回复长度倍数")
    vocabulary_complexity: float = Field(..., ge=0.0, le=1.0, description="词汇复杂度")
    emotional_expressiveness: float = Field(..., ge=0.0, le=1.0, description="情感表达度")

class CognitiveTraits(BaseModel):
    """认知特征"""
    dominant_style: str = Field(..., description="主导认知风格: analytical/intuitive/systematic/creative")
    decision_making_speed: float = Field(..., ge=0.0, le=1.0, description="决策速度")
    risk_tolerance: float = Field(..., ge=0.0, le=1.0, description="风险承受度")

class CharacterEntity(BaseModel):
    """角色相关实体"""
    name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型: person/place/concept/object")
    relationship_type: Optional[str] = Field(None, description="关系类型")
    emotional_valence: float = Field(..., ge=-1.0, le=1.0, description="情感效价")
    importance_score: float = Field(..., ge=0.0, le=1.0, description="重要性评分")
    profile: Dict[str, Any] = Field(default_factory=dict, description="详细档案")

class CharacterBelief(BaseModel):
    """角色信念"""
    statement: str = Field(..., description="信念陈述")
    category: str = Field(..., description="信念类别: moral/political/personal/religious/professional")
    conviction_strength: float = Field(..., ge=0.0, le=1.0, description="信念强度")
    flexibility_score: float = Field(..., ge=0.0, le=1.0, description="灵活性评分")
    origin_context: str = Field(..., description="信念形成背景")
    full_explanation: str = Field(..., description="详细解释")

class CharacterEvent(BaseModel):
    """角色重要事件"""
    title: str = Field(..., description="事件标题")
    age_at_event: int = Field(..., ge=0, le=120, description="事件发生时年龄")
    life_stage: str = Field(..., description="人生阶段: childhood/adolescence/young_adult/adult/elderly")
    event_type: str = Field(..., description="事件类型: achievement/trauma/relationship/career/education")
    emotional_impact: float = Field(..., ge=-1.0, le=1.0, description="情感影响")
    centrality_score: float = Field(..., ge=0.0, le=1.0, description="中心性评分")
    memory_vividness: float = Field(..., ge=0.0, le=1.0, description="记忆清晰度")
    lessons_learned: List[str] = Field(..., description="学到的教训")
    full_narrative: str = Field(..., description="完整叙述")

class FamilyMemberData(BaseModel):
    """家庭成员数据"""
    relationship_type: str = Field(..., description="关系类型: 父亲/母亲/兄长/姐姐等")
    name: str = Field(..., description="姓名")
    personality_summary: Dict[str, float] = Field(..., description="人格摘要")
    parenting_style: Optional[str] = Field(None, description="教养风格(仅父母)")

class CompleteCharacterProfile(BaseModel):
    """完整角色档案"""
    # 基本信息
    target_name: str = Field(..., description="角色姓名")
    description: str = Field(..., description="角色描述")
    age: int = Field(..., ge=16, le=100, description="当前年龄")
    gender: str = Field(..., description="性别")
    occupation: str = Field(..., description="职业")
    
    # 人格特征
    big_five: BigFivePersonality
    communication_style: CommunicationStyle
    cognitive_traits: CognitiveTraits
    
    # 身份背景
    attachment_style: str = Field(..., description="依恋风格: secure/anxious/avoidant/disorganized")
    cultural_background: Dict[str, str] = Field(..., description="文化背景")
    
    # 相关数据
    entities: List[CharacterEntity] = Field(..., min_items=5, max_items=15, description="相关实体")
    beliefs: List[CharacterBelief] = Field(..., min_items=3, max_items=10, description="核心信念")
    events: List[CharacterEvent] = Field(..., min_items=3, max_items=8, description="重要事件")
    family_members: List[FamilyMemberData] = Field(..., min_items=2, max_items=6, description="家庭成员")

class CharacterGenerator:
    """角色生成器"""
    
    def __init__(self):
        # 初始化 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
            raise ValueError("请设置有效的 GEMINI_API_KEY 或 GOOGLE_API_KEY 环境变量")

        genai.configure(api_key=api_key)
        # 使用 instructor patch 客户端
        self.client = instructor.from_gemini(genai.GenerativeModel('gemini-1.5-flash'))
        logger.info("CharacterGenerator initialized successfully with instructor")
    
    def generate_character_prompt(self, character_type: str, additional_context: str = "") -> str:
        """生成角色创建提示词"""
        base_prompt = f"""
请创建一个详细的{character_type}角色档案。这个角色应该是一个真实、立体、有深度的人物。

要求：
1. 角色应该有明确的个性特征和行为模式
2. 背景故事要合理且有说服力
3. 人格特征要相互一致且符合心理学原理
4. 包含足够的细节以支持AI角色扮演

{additional_context}

请确保生成的角色数据完整、一致且富有人性化特征。
"""
        return base_prompt.strip()
    
    async def generate_character(
        self,
        character_type: str = "现代都市青年",
        additional_context: str = ""
    ) -> CompleteCharacterProfile:
        """生成完整的角色档案 (使用 instructor)"""
        try:
            prompt = self.generate_character_prompt(character_type, additional_context)

            # 直接请求 Pydantic 对象
            character = await asyncio.to_thread(
                self.client.chat.completions.create,
                model='gemini-1.5-flash',
                response_model=CompleteCharacterProfile,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.8,
                max_tokens=4000,
            )

            logger.info(
                "Character generated successfully",
                character_name=character.target_name,
                character_type=character_type
            )

            return character

        except Exception as e:
            logger.error("Failed to generate character with instructor", error=str(e))
            raise
    
    async def save_character_to_db(
        self, 
        character: CompleteCharacterProfile, 
        user_id: str
    ) -> str:
        """将角色数据保存到数据库"""
        try:
            async with get_db_session() as db:
                # 创建主要人格档案
                personality = PersonalityProfile(
                    user_id=user_id,
                    target_name=character.target_name,
                    description=character.description,
                    completion_percentage=100.0,  # 自动生成的角色完成度为100%
                    
                    # 大五人格
                    openness_score=character.big_five.openness,
                    conscientiousness_score=character.big_five.conscientiousness,
                    extraversion_score=character.big_five.extraversion,
                    agreeableness_score=character.big_five.agreeableness,
                    neuroticism_score=character.big_five.neuroticism,
                    
                    # 认知特征
                    dominant_cognitive_style=CognitiveStyle(character.cognitive_traits.dominant_style),
                    decision_making_speed=character.cognitive_traits.decision_making_speed,
                    risk_tolerance=character.cognitive_traits.risk_tolerance,
                    
                    # 沟通风格
                    average_response_length=character.communication_style.average_response_length,
                    vocabulary_complexity=character.communication_style.vocabulary_complexity,
                    emotional_expressiveness=character.communication_style.emotional_expressiveness,
                    
                    # 身份背景
                    attachment_style=character.attachment_style,
                    cultural_background=character.cultural_background
                )
                
                db.add(personality)
                await db.flush()  # 获取 personality.profile_id
                
                # 保存相关实体
                for entity_data in character.entities:
                    entity = Entity(
                        personality_id=personality.profile_id,
                        name=entity_data.name,
                        entity_type=entity_data.entity_type,
                        relationship_type=entity_data.relationship_type,
                        emotional_valence=entity_data.emotional_valence,
                        importance_score=entity_data.importance_score,
                        profile=entity_data.profile
                    )
                    db.add(entity)
                
                # 保存信念
                for belief_data in character.beliefs:
                    belief = Belief(
                        personality_id=personality.profile_id,
                        statement=belief_data.statement,
                        belief_category=belief_data.category,
                        conviction_strength=belief_data.conviction_strength,
                        flexibility_score=belief_data.flexibility_score,
                        origin_context=belief_data.origin_context,
                        full_explanation=belief_data.full_explanation
                    )
                    db.add(belief)
                
                # 保存重要事件
                for event_data in character.events:
                    event = Event(
                        personality_id=personality.profile_id,
                        title=event_data.title,
                        age_at_event=event_data.age_at_event,
                        life_stage=event_data.life_stage,
                        event_type=event_data.event_type,
                        emotional_impact=event_data.emotional_impact,
                        centrality_score=event_data.centrality_score,
                        memory_vividness=event_data.memory_vividness,
                        lessons_learned=event_data.lessons_learned,
                        full_narrative=event_data.full_narrative
                    )
                    db.add(event)
                
                # 保存家庭成员
                for family_data in character.family_members:
                    family_member = FamilyMember(
                        personality_id=personality.profile_id,
                        relationship_type=family_data.relationship_type,
                        name=family_data.name,
                        personality_summary=family_data.personality_summary,
                        parenting_style=family_data.parenting_style
                    )
                    db.add(family_member)
                
                await db.commit()
                
                logger.info(
                    "Character saved to database successfully",
                    personality_id=str(personality.profile_id),
                    character_name=character.target_name
                )
                
                return str(personality.profile_id)
                
        except Exception as e:
            logger.error("Failed to save character to database", error=str(e))
            raise

async def main():
    """主函数"""
    print("🎭 角色数据生成器")
    print("=" * 50)
    
    # 初始化数据库
    await init_database()
    
    generator = CharacterGenerator()
    
    # 获取用户输入
    character_type = input("请输入角色类型 (默认: 现代都市青年): ").strip() or "现代都市青年"
    additional_context = input("请输入额外背景信息 (可选): ").strip()
    user_id = input("请输入用户ID (默认: demo用户): ").strip() or "demo"
    
    try:
        print(f"\n🔄 正在生成 '{character_type}' 角色...")
        
        # 生成角色
        character = await generator.generate_character(character_type, additional_context)
        
        print(f"✅ 角色生成成功: {character.target_name}")
        print(f"   描述: {character.description}")
        print(f"   年龄: {character.age}")
        print(f"   职业: {character.occupation}")
        
        # 保存到数据库
        print("\n💾 正在保存到数据库...")
        personality_id = await generator.save_character_to_db(character, user_id)
        
        print(f"✅ 角色保存成功!")
        print(f"   角色ID: {personality_id}")
        print(f"   角色名称: {character.target_name}")
        
        # 显示详细信息
        print(f"\n📊 角色详细信息:")
        print(f"   大五人格: 开放性={character.big_five.openness:.2f}, "
              f"尽责性={character.big_five.conscientiousness:.2f}, "
              f"外向性={character.big_five.extraversion:.2f}")
        print(f"   认知风格: {character.cognitive_traits.dominant_style}")
        print(f"   相关实体数量: {len(character.entities)}")
        print(f"   核心信念数量: {len(character.beliefs)}")
        print(f"   重要事件数量: {len(character.events)}")
        print(f"   家庭成员数量: {len(character.family_members)}")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
</file>

<file path="backend/character_manager.py">
#!/usr/bin/env python3
"""
角色管理脚本
用于查看、管理和测试生成的角色数据
"""

import asyncio
import sys
import os
from typing import List, Dict, Any, Optional
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.db_session import get_db_session
from app.database.models import (
    PersonalityProfile, Entity, Belief, Event, 
    FamilyMember, User, Conversation, Message
)
from app.services.personality_simulator import PersonalitySimulator
from sqlalchemy.future import select
from sqlalchemy import func, desc
import structlog

logger = structlog.get_logger()

class CharacterManager:
    """角色管理器"""
    
    def __init__(self):
        self.simulator = PersonalitySimulator()
    
    async def list_all_characters(self) -> List[Dict[str, Any]]:
        """列出所有角色"""
        try:
            async with get_db_session() as db:
                result = await db.execute(
                    select(PersonalityProfile, User.username)
                    .join(User, PersonalityProfile.user_id == User.user_id)
                    .order_by(desc(PersonalityProfile.created_at))
                )
                
                characters = []
                for personality, username in result.all():
                    characters.append({
                        "id": str(personality.profile_id),
                        "name": personality.target_name,
                        "description": personality.description,
                        "completion": personality.completion_percentage,
                        "created_at": personality.created_at.strftime("%Y-%m-%d %H:%M"),
                        "owner": username,
                        "big_five": {
                            "openness": personality.openness_score,
                            "conscientiousness": personality.conscientiousness_score,
                            "extraversion": personality.extraversion_score,
                            "agreeableness": personality.agreeableness_score,
                            "neuroticism": personality.neuroticism_score
                        }
                    })
                
                return characters
                
        except Exception as e:
            logger.error("Failed to list characters", error=str(e))
            raise
    
    async def get_character_details(self, personality_id: str) -> Dict[str, Any]:
        """获取角色详细信息"""
        try:
            async with get_db_session() as db:
                # 获取基本信息
                result = await db.execute(
                    select(PersonalityProfile).where(
                        PersonalityProfile.profile_id == personality_id
                    )
                )
                personality = result.scalar_one_or_none()
                
                if not personality:
                    raise ValueError(f"角色 {personality_id} 不存在")
                
                # 获取相关实体
                entities_result = await db.execute(
                    select(Entity).where(Entity.personality_id == personality_id)
                )
                entities = entities_result.scalars().all()
                
                # 获取信念
                beliefs_result = await db.execute(
                    select(Belief).where(Belief.personality_id == personality_id)
                )
                beliefs = beliefs_result.scalars().all()
                
                # 获取事件
                events_result = await db.execute(
                    select(Event).where(Event.personality_id == personality_id)
                )
                events = events_result.scalars().all()
                
                # 获取家庭成员
                family_result = await db.execute(
                    select(FamilyMember).where(FamilyMember.personality_id == personality_id)
                )
                family_members = family_result.scalars().all()
                
                return {
                    "personality": personality,
                    "entities": entities,
                    "beliefs": beliefs,
                    "events": events,
                    "family_members": family_members
                }
                
        except Exception as e:
            logger.error("Failed to get character details", error=str(e))
            raise
    
    async def test_character_conversation(
        self, 
        personality_id: str, 
        test_messages: List[str]
    ) -> List[Dict[str, str]]:
        """测试角色对话"""
        try:
            conversation_history = []
            results = []
            
            # 创建临时对话ID
            temp_conversation_id = f"test_{personality_id}"
            
            for i, user_input in enumerate(test_messages):
                print(f"\n👤 用户: {user_input}")
                
                # 生成AI回复
                ai_response = await self.simulator.generate_response(
                    personality_id=personality_id,
                    user_input=user_input,
                    db=None,  # 测试模式不需要保存
                    conversation_id=temp_conversation_id,
                    conversation_history=conversation_history
                )
                
                print(f"🤖 AI: {ai_response}")
                
                # 更新对话历史
                conversation_history.append(f"user: {user_input}")
                conversation_history.append(f"ai: {ai_response}")
                
                results.append({
                    "user": user_input,
                    "ai": ai_response
                })
            
            return results
            
        except Exception as e:
            logger.error("Failed to test character conversation", error=str(e))
            raise
    
    async def delete_character(self, personality_id: str) -> bool:
        """删除角色"""
        try:
            async with get_db_session() as db:
                # 删除相关数据
                await db.execute(
                    select(Entity).where(Entity.personality_id == personality_id)
                )
                await db.execute(
                    select(Belief).where(Belief.personality_id == personality_id)
                )
                await db.execute(
                    select(Event).where(Event.personality_id == personality_id)
                )
                await db.execute(
                    select(FamilyMember).where(FamilyMember.personality_id == personality_id)
                )
                
                # 删除主记录
                result = await db.execute(
                    select(PersonalityProfile).where(
                        PersonalityProfile.profile_id == personality_id
                    )
                )
                personality = result.scalar_one_or_none()
                
                if personality:
                    await db.delete(personality)
                    await db.commit()
                    return True
                
                return False
                
        except Exception as e:
            logger.error("Failed to delete character", error=str(e))
            raise
    
    def print_character_list(self, characters: List[Dict[str, Any]]):
        """打印角色列表"""
        if not characters:
            print("📭 暂无角色数据")
            return
        
        print(f"\n📋 角色列表 (共 {len(characters)} 个):")
        print("-" * 80)
        
        for i, char in enumerate(characters, 1):
            print(f"{i:2d}. {char['name']} ({char['id'][:8]}...)")
            print(f"     描述: {char['description'][:50]}...")
            print(f"     完成度: {char['completion']:.1f}%")
            print(f"     创建时间: {char['created_at']}")
            print(f"     大五人格: O={char['big_five']['openness']:.2f} "
                  f"C={char['big_five']['conscientiousness']:.2f} "
                  f"E={char['big_five']['extraversion']:.2f}")
            print()
    
    def print_character_details(self, details: Dict[str, Any]):
        """打印角色详细信息"""
        personality = details["personality"]
        
        print(f"\n🎭 角色详细信息: {personality.target_name}")
        print("=" * 60)
        
        print(f"ID: {personality.profile_id}")
        print(f"描述: {personality.description}")
        print(f"完成度: {personality.completion_percentage}%")
        print(f"创建时间: {personality.created_at}")
        
        print(f"\n📊 大五人格:")
        print(f"   开放性: {personality.openness_score:.2f}")
        print(f"   尽责性: {personality.conscientiousness_score:.2f}")
        print(f"   外向性: {personality.extraversion_score:.2f}")
        print(f"   宜人性: {personality.agreeableness_score:.2f}")
        print(f"   神经质: {personality.neuroticism_score:.2f}")
        
        print(f"\n🧠 认知特征:")
        print(f"   主导风格: {personality.dominant_cognitive_style}")
        print(f"   决策速度: {personality.decision_making_speed:.2f}")
        print(f"   风险承受: {personality.risk_tolerance:.2f}")
        
        print(f"\n💬 沟通风格:")
        print(f"   回复长度: {personality.average_response_length:.2f}")
        print(f"   词汇复杂度: {personality.vocabulary_complexity:.2f}")
        print(f"   情感表达: {personality.emotional_expressiveness:.2f}")
        
        print(f"\n👥 相关实体 ({len(details['entities'])} 个):")
        for entity in details["entities"][:5]:  # 只显示前5个
            print(f"   • {entity.name} ({entity.entity_type}) - 重要性: {entity.importance_score:.2f}")
        
        print(f"\n💭 核心信念 ({len(details['beliefs'])} 个):")
        for belief in details["beliefs"][:3]:  # 只显示前3个
            print(f"   • {belief.statement[:50]}... (强度: {belief.conviction_strength:.2f})")
        
        print(f"\n📅 重要事件 ({len(details['events'])} 个):")
        for event in details["events"][:3]:  # 只显示前3个
            print(f"   • {event.title} (年龄{event.age_at_event}) - 影响: {event.emotional_impact:.2f}")
        
        print(f"\n👨‍👩‍👧‍👦 家庭成员 ({len(details['family_members'])} 个):")
        for member in details["family_members"]:
            print(f"   • {member.name} ({member.relationship_type})")

async def main():
    """主函数"""
    print("🎭 角色管理器")
    print("=" * 40)
    
    manager = CharacterManager()
    
    while True:
        print("\n请选择操作:")
        print("1. 列出所有角色")
        print("2. 查看角色详情")
        print("3. 测试角色对话")
        print("4. 删除角色")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        try:
            if choice == "0":
                print("👋 再见!")
                break
                
            elif choice == "1":
                characters = await manager.list_all_characters()
                manager.print_character_list(characters)
                
            elif choice == "2":
                personality_id = input("请输入角色ID: ").strip()
                details = await manager.get_character_details(personality_id)
                manager.print_character_details(details)
                
            elif choice == "3":
                personality_id = input("请输入角色ID: ").strip()
                
                print("请输入测试消息 (输入空行结束):")
                test_messages = []
                while True:
                    msg = input("消息: ").strip()
                    if not msg:
                        break
                    test_messages.append(msg)
                
                if test_messages:
                    print(f"\n🧪 开始测试对话...")
                    results = await manager.test_character_conversation(
                        personality_id, test_messages
                    )
                    print(f"\n✅ 对话测试完成!")
                else:
                    print("❌ 没有输入测试消息")
                
            elif choice == "4":
                personality_id = input("请输入要删除的角色ID: ").strip()
                confirm = input(f"确认删除角色 {personality_id}? (y/N): ").strip().lower()
                
                if confirm == 'y':
                    success = await manager.delete_character(personality_id)
                    if success:
                        print("✅ 角色删除成功")
                    else:
                        print("❌ 角色不存在或删除失败")
                else:
                    print("❌ 取消删除")
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print(f"\n⚠️  操作中断")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
</file>

<file path="backend/init_db.py">
"""
数据库初始化脚本
"""

import asyncio
import os
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv

from app.database.models import Base
from app.database.db_session import db_manager

load_dotenv()

async def init_database():
    """初始化数据库表"""
    try:
        print("🔄 正在初始化数据库...")
        
        # 创建所有表
        await db_manager.create_tables()
        
        print("✅ 数据库表创建成功")
        
        # 这里可以添加初始数据
        print("📊 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(init_database())
</file>

<file path="backend/main.py">
"""
Enhanced FastAPI application for 100% personality cloning system
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from pydantic import BaseModel
from jose import JWTError, jwt
import structlog

from app.database.db_session import get_db_session, init_database, cleanup_database
from app.database.models import User, PersonalityProfile, Conversation, Message
# 导入模拟API
from app.api.endpoints import simulation

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Application lifecycle management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting personality cloning system")
    await init_database()
    yield
    # Shutdown
    logger.info("Shutting down personality cloning system")
    await cleanup_database()

# Create FastAPI app
app = FastAPI(
    title="100% Personality Cloning System",
    description="Advanced AI system for complete personality replication",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# --- JWT Configuration ---
SECRET_KEY = os.getenv("SECRET_KEY", "your-default-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
# --- End JWT Configuration ---

# Include API routers
app.include_router(simulation.router, prefix="/api/v1", tags=["Simulation"])

# === Pydantic Models ===

class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class PersonalityCreate(BaseModel):
    target_name: str
    description: Optional[str] = None

# 保留PersonalityCreate用于手动创建角色档案

# === Authentication ===

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """Get current authenticated user from JWT token"""
    token = credentials.credentials
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    result = await db.execute(select(User).where(User.username == username))
    user = result.scalar_one_or_none()

    if user is None or not user.is_active:
        raise credentials_exception

    return user

# === API Endpoints ===

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "100% Personality Cloning System is running",
        "version": "1.0.0",
        "status": "healthy"
    }

# 注册端点已删除，使用批量生成脚本创建演示用户

@app.post("/auth/login")
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db_session)
):
    """Login user"""
    try:
        # Find user
        result = await db.execute(
            select(User).where(User.username == login_data.username)
        )
        user = result.scalar_one_or_none()

        if not user or user.hashed_password != login_data.password:  # Simplified check
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        logger.info("User logged in", user_id=str(user.user_id))

        # 创建JWT Token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": str(user.user_id)},
            expires_delta=access_token_expires,
        )

        return {
            "message": "Login successful",
            "user_id": str(user.user_id),
            "token": access_token,  # 返回JWT Token
            "token_type": "bearer",
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.post("/personalities")
async def create_personality(
    personality_data: PersonalityCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Create a new personality profile"""
    try:
        new_personality = PersonalityProfile(
            user_id=current_user.user_id,
            target_name=personality_data.target_name,
            description=personality_data.description
        )
        
        db.add(new_personality)
        await db.commit()
        await db.refresh(new_personality)
        
        logger.info(
            "Personality profile created",
            personality_id=str(new_personality.profile_id),
            target_name=personality_data.target_name
        )
        
        return {
            "message": "Personality profile created",
            "personality_id": str(new_personality.profile_id),
            "target_name": personality_data.target_name,
            "completion_percentage": 0.0
        }
        
    except Exception as e:
        logger.error("Personality creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create personality profile"
        )

@app.get("/personalities")
async def list_personalities(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """List user's personality profiles"""
    try:
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.user_id == current_user.user_id)
        )
        personalities = result.scalars().all()
        
        return [
            {
                "personality_id": str(p.profile_id),
                "target_name": p.target_name,
                "description": p.description,
                "completion_percentage": p.completion_percentage,
                "created_at": p.created_at.isoformat() if p.created_at else None
            }
            for p in personalities
        ]
        
    except Exception as e:
        logger.error("Failed to list personalities", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve personality profiles"
        )

@app.get("/personalities/{personality_id}")
async def get_personality_detail(
    personality_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """获取单个人格档案的完整详情"""
    try:
        result = await db.execute(
            select(PersonalityProfile)
            .where(
                PersonalityProfile.profile_id == personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
            .options(
                selectinload(PersonalityProfile.events),
                selectinload(PersonalityProfile.beliefs),
                selectinload(PersonalityProfile.entities),
                selectinload(PersonalityProfile.family_members)
            )
        )
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Personality profile not found")

        # 将数据格式化为前端需要的结构
        return {
            "profile_id": str(profile.profile_id),
            "target_name": profile.target_name,
            "description": profile.description,
            "big_five": {
                "openness": profile.openness_score or 0.5,
                "conscientiousness": profile.conscientiousness_score or 0.5,
                "extraversion": profile.extraversion_score or 0.5,
                "agreeableness": profile.agreeableness_score or 0.5,
                "neuroticism": profile.neuroticism_score or 0.5,
            },
            "attachment_style": profile.attachment_style,
            "cultural_background": profile.cultural_background,
            "events": [
                {"title": e.title, "age": e.age_at_event, "narrative": e.full_narrative}
                for e in profile.events
            ],
            "beliefs": [
                {"statement": b.statement, "explanation": b.full_explanation}
                for b in profile.beliefs
            ],
            "family_members": [
                {"relationship": fm.relationship_type, "summary": fm.personality_summary}
                for fm in profile.family_members
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get personality detail", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve profile details")

# 用户对话相关的API端点已删除，现在只保留AI角色对话功能

# /chat/respond 端点已删除

# /predict 和 /validate 端点已删除，现在专注于AI角色对话功能

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
</file>

<file path="backend/requirements.txt">
# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
aiosqlite==0.19.0
alembic==1.13.1

# AI/ML
google-generativeai==0.8.0
openai==1.54.0
instructor==1.6.0
transformers==4.46.0
torch>=2.6.0
sentence-transformers==3.3.0
numpy>=1.24.0

# Text analysis
textstat==0.7.3
spacy==3.7.2
nltk==3.8.1

# Audio analysis (optional)
azure-cognitiveservices-speech==1.34.0
librosa==0.10.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
httpx==0.25.2
aiofiles==23.2.1
python-dateutil==2.8.2

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
mypy==1.7.1
</file>

<file path="backend/sql/init.sql">
-- 100% 人格复刻系统数据库初始化脚本
-- 创建必要的扩展和初始数据

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用全文搜索扩展
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建枚举类型
DO $$ BEGIN
    CREATE TYPE personality_dimension AS ENUM (
        'openness', 'conscientiousness', 'extraversion', 
        'agreeableness', 'neuroticism'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE cognitive_style AS ENUM (
        'analytical', 'intuitive', 'systematic', 'creative'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE emotional_state AS ENUM (
        'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'neutral'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建索引函数（在表创建后执行）
CREATE OR REPLACE FUNCTION create_personality_indexes() RETURNS void AS $$
BEGIN
    -- 用户表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_username') THEN
        CREATE INDEX idx_users_username ON users(username);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_email') THEN
        CREATE INDEX idx_users_email ON users(email);
    END IF;

    -- 人格档案表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_user_id') THEN
        CREATE INDEX idx_personality_profiles_user_id ON personality_profiles(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_target_name') THEN
        CREATE INDEX idx_personality_profiles_target_name ON personality_profiles(target_name);
    END IF;

    -- 实体表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_personality_id') THEN
        CREATE INDEX idx_entities_personality_id ON entities(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_name') THEN
        CREATE INDEX idx_entities_name ON entities(name);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_type') THEN
        CREATE INDEX idx_entities_entity_type ON entities(entity_type);
    END IF;

    -- 信念表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_personality_id') THEN
        CREATE INDEX idx_beliefs_personality_id ON beliefs(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_category') THEN
        CREATE INDEX idx_beliefs_category ON beliefs(belief_category);
    END IF;

    -- 事件表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_personality_id') THEN
        CREATE INDEX idx_events_personality_id ON events(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_age') THEN
        CREATE INDEX idx_events_age ON events(age_at_event);
    END IF;

    -- 对话表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_user_id') THEN
        CREATE INDEX idx_conversations_user_id ON conversations(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_personality_id') THEN
        CREATE INDEX idx_conversations_personality_id ON conversations(personality_id);
    END IF;

    -- 消息表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_conversation_id') THEN
        CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_timestamp') THEN
        CREATE INDEX idx_messages_timestamp ON messages(timestamp);
    END IF;

    -- 全文搜索索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_statement_gin') THEN
        CREATE INDEX idx_beliefs_statement_gin ON beliefs USING gin(statement gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_narrative_gin') THEN
        CREATE INDEX idx_events_narrative_gin ON events USING gin(full_narrative gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_content_gin') THEN
        CREATE INDEX idx_messages_content_gin ON messages USING gin(content gin_trgm_ops);
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 创建触发器函数用于更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建用于计算人格完成度的函数
CREATE OR REPLACE FUNCTION calculate_personality_completion(profile_id UUID)
RETURNS FLOAT AS $$
DECLARE
    completion_score FLOAT := 0;
    entity_count INTEGER;
    belief_count INTEGER;
    event_count INTEGER;
    cognitive_pattern_count INTEGER;
BEGIN
    -- 计算各个维度的完成度
    
    -- 基础信息 (20%)
    SELECT CASE 
        WHEN target_name IS NOT NULL AND target_name != '' THEN 0.2 
        ELSE 0 
    END INTO completion_score
    FROM personality_profiles 
    WHERE profile_id = calculate_personality_completion.profile_id;
    
    -- 实体关系 (25%)
    SELECT COUNT(*) INTO entity_count
    FROM entities 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(entity_count * 0.05, 0.25);
    
    -- 信念系统 (25%)
    SELECT COUNT(*) INTO belief_count
    FROM beliefs 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(belief_count * 0.08, 0.25);
    
    -- 生活事件 (20%)
    SELECT COUNT(*) INTO event_count
    FROM events 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(event_count * 0.04, 0.20);
    
    -- 认知模式 (10%)
    SELECT COUNT(*) INTO cognitive_pattern_count
    FROM cognitive_patterns 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(cognitive_pattern_count * 0.02, 0.10);
    
    RETURN LEAST(completion_score * 100, 100);
END;
$$ LANGUAGE plpgsql;

-- 创建用于搜索相似人格的函数
CREATE OR REPLACE FUNCTION find_similar_personalities(
    target_profile_id UUID,
    similarity_threshold FLOAT DEFAULT 0.7
)
RETURNS TABLE(
    profile_id UUID,
    target_name VARCHAR,
    similarity_score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.profile_id,
        p.target_name,
        (
            1.0 - (
                ABS(p.openness_score - tp.openness_score) +
                ABS(p.conscientiousness_score - tp.conscientiousness_score) +
                ABS(p.extraversion_score - tp.extraversion_score) +
                ABS(p.agreeableness_score - tp.agreeableness_score) +
                ABS(p.neuroticism_score - tp.neuroticism_score)
            ) / 5.0
        ) as similarity_score
    FROM personality_profiles p
    CROSS JOIN personality_profiles tp
    WHERE tp.profile_id = target_profile_id
    AND p.profile_id != target_profile_id
    AND (
        1.0 - (
            ABS(p.openness_score - tp.openness_score) +
            ABS(p.conscientiousness_score - tp.conscientiousness_score) +
            ABS(p.extraversion_score - tp.extraversion_score) +
            ABS(p.agreeableness_score - tp.agreeableness_score) +
            ABS(p.neuroticism_score - tp.neuroticism_score)
        ) / 5.0
    ) >= similarity_threshold
    ORDER BY similarity_score DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建演示数据插入函数
CREATE OR REPLACE FUNCTION insert_demo_data() RETURNS void AS $$
DECLARE
    demo_user_id UUID;
    demo_personality_id UUID;
BEGIN
    -- 检查是否已存在演示数据
    IF EXISTS (SELECT 1 FROM users WHERE username = 'demo') THEN
        RETURN;
    END IF;

    -- 创建演示用户
    INSERT INTO users (username, email, hashed_password)
    VALUES ('demo', '<EMAIL>', 'demo123')
    RETURNING user_id INTO demo_user_id;

    -- 创建演示人格档案
    INSERT INTO personality_profiles (
        user_id, target_name, description,
        openness_score, conscientiousness_score, extraversion_score,
        agreeableness_score, neuroticism_score,
        decision_making_speed, risk_tolerance,
        average_response_length, vocabulary_complexity, emotional_expressiveness
    )
    VALUES (
        demo_user_id, '演示人格', '这是一个演示用的人格档案',
        0.7, 0.6, 0.8, 0.9, 0.3,
        0.6, 0.4,
        150.0, 0.7, 0.8
    )
    RETURNING profile_id INTO demo_personality_id;

    -- 插入一些演示实体
    INSERT INTO entities (personality_id, name, entity_type, relationship_type, emotional_valence, importance_score)
    VALUES 
        (demo_personality_id, '家人', 'person', 'family', 0.9, 0.95),
        (demo_personality_id, '朋友', 'person', 'friend', 0.8, 0.8),
        (demo_personality_id, '工作', 'concept', 'professional', 0.6, 0.7);

    -- 插入一些演示信念
    INSERT INTO beliefs (personality_id, statement, belief_category, conviction_strength, flexibility_score)
    VALUES 
        (demo_personality_id, '诚实是最重要的品质', 'moral', 0.9, 0.2),
        (demo_personality_id, '努力工作会有回报', 'personal', 0.8, 0.4),
        (demo_personality_id, '家庭比事业更重要', 'personal', 0.7, 0.6);

    -- 插入一些演示事件
    INSERT INTO events (personality_id, title, age_at_event, life_stage, event_type, emotional_impact, centrality_score, memory_vividness)
    VALUES 
        (demo_personality_id, '大学毕业', 22, 'young_adult', 'achievement', 0.8, 0.7, 0.9),
        (demo_personality_id, '第一份工作', 23, 'young_adult', 'professional', 0.6, 0.6, 0.8),
        (demo_personality_id, '结婚', 28, 'adult', 'relationship', 0.9, 0.9, 0.95);

END;
$$ LANGUAGE plpgsql;

-- 数据库初始化完成后的通知
DO $$
BEGIN
    RAISE NOTICE '人格复刻系统数据库初始化完成';
    RAISE NOTICE '- 已创建必要的扩展和类型';
    RAISE NOTICE '- 已定义索引创建函数';
    RAISE NOTICE '- 已创建工具函数';
    RAISE NOTICE '- 准备插入演示数据';
END $$;
</file>

<file path="docker-compose.yml">
services:
  postgres:
    image: postgres:16
    container_name: personality_clone_postgres
    environment:
      POSTGRES_USER: user # 注意：生产环境应使用更安全的凭证
      POSTGRES_PASSWORD: password # 注意：生产环境应使用更安全的凭证
      POSTGRES_DB: personality_clone_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
</file>

<file path="frontend/.env.example">
# 前端环境变量配置

# API基础URL
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_TITLE=人格复刻系统
VITE_APP_VERSION=1.0.0

# 开发配置
VITE_DEV_MODE=true
VITE_ENABLE_MOCK=false

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true
</file>

<file path="frontend/index.html">
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>100% 人格复刻系统</title>
    <meta name="description" content="基于AI的完整人格复刻与分析系统" />
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
                     'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
                     'Noto Color Emoji';
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载状态 -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载人格复刻系统...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
</file>

<file path="frontend/package.json">
{
  "name": "personality-clone-frontend",
  "version": "1.0.0",
  "description": "Frontend for 100% Personality Cloning System",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "@element-plus/icons-vue": "^2.1.0",
    "axios": "^1.6.2",
    "d3": "^7.8.5",
    "dayjs": "^1.11.10",
    "echarts": "^5.6.0",
    "element-plus": "^2.4.4",
    "highlight.js": "^11.9.0",
    "marked": "^9.1.6",
    "pinia": "^2.1.7",
    "vue": "^3.3.8",
    "vue-echarts": "^6.6.1",
    "vue-router": "^4.2.5"
  },
  "devDependencies": {
    "@types/d3": "^7.4.3",
    "@vitejs/plugin-vue": "^4.5.0",
    "eslint": "^8.54.0",
    "eslint-plugin-vue": "^9.18.1",
    "prettier": "^3.1.0",
    "vite": "^5.0.0"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
</file>

<file path="frontend/src/App.vue">
<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <!-- 全局加载遮罩 -->
      <el-loading
        v-loading="globalLoading"
        element-loading-text="正在处理中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-spinner="el-icon-loading"
      >
        <!-- 主要内容区域 -->
        <div class="app-container">
          <!-- 顶部导航栏 -->
          <AppHeader v-if="showHeader" />
          
          <!-- 侧边栏 -->
          <AppSidebar v-if="showSidebar" />
          
          <!-- 主内容区 -->
          <main class="main-content" :class="{ 'with-sidebar': showSidebar }">
            <router-view v-slot="{ Component, route }">
              <transition name="fade" mode="out-in">
                <component :is="Component" :key="route.path" />
              </transition>
            </router-view>
          </main>
          
          <!-- 全局通知 -->
          <GlobalNotifications />
        </div>
      </el-loading>
    </el-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import AppHeader from './components/layout/AppHeader.vue'
import AppSidebar from './components/layout/AppSidebar.vue'
import GlobalNotifications from './components/common/GlobalNotifications.vue'

import { useAuthStore } from './stores/auth'
import { useAppStore } from './stores/app'

// 状态管理
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()

// 响应式数据
const locale = ref(zhCn)
const globalLoading = computed(() => appStore.globalLoading)

// 计算属性
const showHeader = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideHeader
})

const showSidebar = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideSidebar
})

// 生命周期
onMounted(async () => {
  // 初始化应用
  await initializeApp()
})

// 监听路由变化
watch(route, (to) => {
  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
})

// 方法
const initializeApp = async () => {
  try {
    appStore.setGlobalLoading(true)
    
    // 检查认证状态
    await authStore.checkAuth()
    
    // 初始化应用设置
    await appStore.initializeApp()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setGlobalLoading(false)
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.main-content {
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
  padding: 20px;
}

.main-content.with-sidebar {
  margin-left: 250px;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }
  
  .main-content {
    padding: 10px;
  }
}

/* 全局样式 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

/* 自定义滚动条 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background-color: #a8a8a8;
}
</style>

<style>
/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

#app {
  height: 100%;
}

/* 自定义主题色 */
:root {
  --el-color-primary: #667eea;
  --el-color-primary-light-3: #8b9df0;
  --el-color-primary-light-5: #a6b5f3;
  --el-color-primary-light-7: #c1cdf6;
  --el-color-primary-light-8: #d1ddf8;
  --el-color-primary-light-9: #e8edfb;
  --el-color-primary-dark-2: #5a72e8;
}

/* 动画效果 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.full-width {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
</file>

<file path="frontend/src/components/common/GlobalNotifications.vue">
<template>
  <div class="global-notifications">
    <!-- 这个组件用于显示全局通知，Element Plus 的 ElMessage 会自动处理 -->
  </div>
</template>

<script setup>
// 这个组件主要用于全局通知的容器
// Element Plus 的消息组件会自动挂载到 body
</script>

<style scoped>
.global-notifications {
  /* 预留给全局通知的样式 */
}
</style>
</file>

<file path="frontend/src/components/layout/AppHeader.vue">
<template>
  <header class="app-header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon size="20"><Menu /></el-icon>
      </el-button>
      
      <div class="logo">
        <span class="logo-text">人格复刻系统</span>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 通知 -->
      <el-dropdown trigger="click" class="notification-dropdown">
        <el-button type="text" class="header-button">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon size="18"><Bell /></el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="notification-header">
              <span>通知</span>
              <el-button type="text" size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notifications.slice(0, 5)"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
                </div>
              </div>
              <div v-if="notifications.length === 0" class="no-notifications">
                暂无通知
              </div>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 主题切换 -->
      <el-button
        type="text"
        class="header-button"
        @click="toggleTheme"
      >
        <el-icon size="18">
          <component :is="isDarkTheme ? 'Sunny' : 'Moon'" />
        </el-icon>
      </el-button>
      
      <!-- 用户菜单 -->
      <el-dropdown trigger="click">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            {{ userInfo.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <span class="username">{{ userInfo.username }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="goToSettings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Menu, Bell, Sunny, Moon, ArrowDown, Setting, SwitchButton
} from '@element-plus/icons-vue'

import { useAuthStore } from '../../stores/auth'
import { useAppStore } from '../../stores/app'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const notifications = computed(() => appStore.notifications)
const unreadCount = computed(() => appStore.unreadNotifications)
const isDarkTheme = computed(() => appStore.isDarkTheme)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const markAllAsRead = () => {
  appStore.markAllNotificationsAsRead()
}

const formatTime = (time) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now - notificationTime
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const goToSettings = () => {
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await authStore.logout()
    if (result.success) {
      router.push('/login')
    }
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 15px;
  color: #606266;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-button {
  color: #606266;
  padding: 8px;
}

.header-button:hover {
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.username {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
}

/* 通知相关样式 */
.notification-dropdown :deep(.el-dropdown-menu) {
  width: 320px;
  max-height: 400px;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #2c3e50;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-content {
  width: 100%;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.no-notifications {
  padding: 40px 16px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 15px;
  }
  
  .username {
    display: none;
  }
  
  .notification-dropdown :deep(.el-dropdown-menu) {
    width: 280px;
  }
}

/* 深色模式 */
.dark .app-header {
  background: #1f2937;
  border-bottom-color: #374151;
}

.dark .logo-text {
  color: #f9fafb;
}

.dark .header-button {
  color: #d1d5db;
}

.dark .header-button:hover {
  color: #60a5fa;
}

.dark .user-info:hover {
  background-color: #374151;
}

.dark .username {
  color: #f9fafb;
}
</style>
</file>

<file path="frontend/src/components/layout/AppSidebar.vue">
<template>
  <aside class="app-sidebar" :class="{ collapsed: sidebarCollapsed }">
    <div class="sidebar-content">
      <el-menu
        :default-active="activeMenu"
        :collapse="sidebarCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
          @click="handleMenuClick(route)"
        >
          <el-icon>
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <el-button
        type="text"
        class="collapse-button"
        @click="toggleSidebar"
      >
        <el-icon>
          <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
        <span v-if="!sidebarCollapsed">收起菜单</span>
      </el-button>
    </div>
  </aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Expand, Fold } from '@element-plus/icons-vue'

import { useAppStore } from '../../stores/app'
import { menuRoutes } from '../../router'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const activeMenu = computed(() => {
  // 根据当前路由确定激活的菜单项
  const path = route.path
  
  // 精确匹配
  if (menuRoutes.find(r => r.path === path)) {
    return path
  }
  
  // 模糊匹配（用于子路由）
  for (const menuRoute of menuRoutes) {
    if (path.startsWith(menuRoute.path) && menuRoute.path !== '/') {
      return menuRoute.path
    }
  }
  
  return '/'
})

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const handleMenuClick = (route) => {
  // 可以在这里添加菜单点击的额外逻辑
  console.log('菜单点击:', route.meta.title)
}
</script>

<style scoped>
.app-sidebar {
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  width: 250px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.05);
}

.app-sidebar.collapsed {
  width: 64px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f0f9ff;
  color: #409eff;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item .el-icon) {
  margin-right: 12px;
  font-size: 18px;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item) {
  padding: 0 20px;
  text-align: center;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item .el-icon) {
  margin-right: 0;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.collapse-button {
  width: 100%;
  justify-content: flex-start;
  color: #606266;
  font-size: 14px;
}

.collapse-button:hover {
  color: #409eff;
  background-color: #f0f9ff;
}

.app-sidebar.collapsed .collapse-button {
  justify-content: center;
}

.app-sidebar.collapsed .collapse-button span {
  display: none;
}

/* 自定义滚动条 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* 移动端遮罩 */
  .app-sidebar:not(.collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

/* 深色模式 */
.dark .app-sidebar {
  background: #1f2937;
  border-right-color: #374151;
}

.dark .sidebar-menu :deep(.el-menu-item) {
  color: #d1d5db;
}

.dark .sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #374151;
  color: #60a5fa;
}

.dark .sidebar-footer {
  border-top-color: #374151;
}

.dark .collapse-button {
  color: #d1d5db;
}

.dark .collapse-button:hover {
  color: #60a5fa;
  background-color: #374151;
}
</style>
</file>

<file path="frontend/src/main.js">
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 创建应用实例
const app = createApp(App)

// 注册 Pinia 状态管理
const pinia = createPinia()
app.use(pinia)

// 注册路由
app.use(router)

// 注册 Element Plus
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 这里可以添加错误上报逻辑
}

// 全局属性
app.config.globalProperties.$ELEMENT = { size: 'default' }

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 尝试从本地存储恢复认证状态
    await authStore.checkAuth()
    
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 人格复刻系统启动成功')
  console.log('📊 当前环境:', import.meta.env.MODE)
  console.log('🔗 API地址:', import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
}
</file>

<file path="frontend/src/router/index.js">
import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Login = () => import('../views/auth/Login.vue')
const Register = () => import('../views/auth/Register.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const PersonalityList = () => import('../views/personality/PersonalityList.vue')
const PersonalityDetail = () => import('../views/personality/PersonalityDetail.vue')
const PersonalityCreate = () => import('../views/personality/PersonalityCreate.vue')
const ChatInterface = () => import('../views/chat/ChatInterface.vue')
const NotFound = () => import('../views/error/NotFound.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiresAuth: true,
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    component: PersonalityList,
    meta: {
      title: '人格档案',
      requiresAuth: true,
      icon: 'User'
    }
  },
  {
    path: '/personalities/create',
    name: 'PersonalityCreate',
    component: PersonalityCreate,
    meta: {
      title: '创建人格档案',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '创建档案' }
      ]
    }
  },
  {
    path: '/personalities/:id',
    name: 'PersonalityDetail',
    component: PersonalityDetail,
    meta: {
      title: '人格详情',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '详情' }
      ]
    }
  },
  {
    path: '/chat/:personalityId?',
    name: 'ChatInterface',
    component: ChatInterface,
    meta: {
      title: 'AI角色对话',
      requiresAuth: true,
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      hideHeader: true,
      hideSidebar: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出菜单配置
export const menuRoutes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: {
      title: '仪表板',
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    meta: {
      title: '人格档案',
      icon: 'User'
    }
  },
  {
    path: '/chat',
    name: 'ChatInterface',
    meta: {
      title: 'AI角色对话',
      icon: 'ChatDotRound'
    }
  }
]
</file>

<file path="frontend/src/stores/app.js">
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const globalLoading = ref(false)
  const sidebarCollapsed = ref(false)
  const theme = ref(localStorage.getItem('theme') || 'light')
  const language = ref(localStorage.getItem('language') || 'zh-CN')
  const notifications = ref([])
  const appSettings = ref({
    autoSave: true,
    soundEnabled: true,
    animationsEnabled: true,
    compactMode: false
  })

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 动作
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML类名以应用主题
    document.documentElement.className = newTheme === 'dark' ? 'dark' : ''
  }

  const toggleTheme = () => {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }

  const setLanguage = (newLanguage) => {
    language.value = newLanguage
    localStorage.setItem('language', newLanguage)
  }

  const addNotification = (notification) => {
    const id = Date.now().toString()
    notifications.value.unshift({
      id,
      timestamp: new Date(),
      read: false,
      ...notification
    })
    
    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markNotificationAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const updateAppSettings = (newSettings) => {
    appSettings.value = { ...appSettings.value, ...newSettings }
    localStorage.setItem('appSettings', JSON.stringify(appSettings.value))
  }

  const initializeApp = async () => {
    try {
      // 恢复侧边栏状态
      const savedSidebarState = localStorage.getItem('sidebarCollapsed')
      if (savedSidebarState !== null) {
        sidebarCollapsed.value = savedSidebarState === 'true'
      }

      // 恢复应用设置
      const savedSettings = localStorage.getItem('appSettings')
      if (savedSettings) {
        appSettings.value = { ...appSettings.value, ...JSON.parse(savedSettings) }
      }

      // 应用主题
      document.documentElement.className = theme.value === 'dark' ? 'dark' : ''

      // 初始化通知（可以从服务器获取）
      // await loadNotifications()

    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  const showMessage = (message, type = 'info') => {
    addNotification({
      type,
      title: type === 'error' ? '错误' : type === 'success' ? '成功' : '提示',
      message,
      duration: type === 'error' ? 0 : 3000
    })
  }

  const showSuccess = (message) => {
    showMessage(message, 'success')
  }

  const showError = (message) => {
    showMessage(message, 'error')
  }

  const showWarning = (message) => {
    showMessage(message, 'warning')
  }

  const showInfo = (message) => {
    showMessage(message, 'info')
  }

  // 响应式断点
  const breakpoints = ref({
    xs: window.matchMedia('(max-width: 575px)'),
    sm: window.matchMedia('(min-width: 576px) and (max-width: 767px)'),
    md: window.matchMedia('(min-width: 768px) and (max-width: 991px)'),
    lg: window.matchMedia('(min-width: 992px) and (max-width: 1199px)'),
    xl: window.matchMedia('(min-width: 1200px)')
  })

  const isMobile = computed(() => breakpoints.value.xs.matches)
  const isTablet = computed(() => breakpoints.value.sm.matches || breakpoints.value.md.matches)
  const isDesktop = computed(() => breakpoints.value.lg.matches || breakpoints.value.xl.matches)

  return {
    // 状态
    globalLoading,
    sidebarCollapsed,
    theme,
    language,
    notifications,
    appSettings,
    breakpoints,

    // 计算属性
    isDarkTheme,
    unreadNotifications,
    isMobile,
    isTablet,
    isDesktop,

    // 动作
    setGlobalLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    setLanguage,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    clearAllNotifications,
    updateAppSettings,
    initializeApp,
    showMessage,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
</file>

<file path="frontend/src/stores/auth.js">
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value || {})

  // 动作
  const login = async (credentials) => {
    try {
      isLoading.value = true

      const response = await api.post('/auth/login', credentials)
      // 修改这里以匹配新的响应格式
      const { token: newToken, user_id, message } = response.data

      token.value = newToken
      user.value = { user_id, username: credentials.username }

      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))

      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`

      ElMessage.success(message || '登录成功')

      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      const message = error.response?.data?.detail || '登录失败，请检查用户名和密码'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/auth/register', userData)
      const { token: newToken, user_id, message } = response.data
      
      // 注册成功后自动登录
      token.value = newToken
      user.value = { user_id, username: userData.username, email: userData.email }
      
      // 持久化存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      ElMessage.success(message || '注册成功')
      
      return { success: true }
    } catch (error) {
      console.error('注册失败:', error)
      const message = error.response?.data?.detail || '注册失败，请稍后重试'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // 清除本地状态
      user.value = null
      token.value = null
      
      // 清除持久化存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 清除API默认header
      delete api.defaults.headers.common['Authorization']
      
      ElMessage.success('已安全退出')
      
      return { success: true }
    } catch (error) {
      console.error('退出失败:', error)
      return { success: false }
    }
  }

  const checkAuth = async () => {
    try {
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')
      
      if (!storedToken || !storedUser) {
        return false
      }
      
      // 恢复状态
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
      
      // 验证token有效性（可选）
      // 这里可以调用一个验证接口来确认token是否仍然有效
      
      return true
    } catch (error) {
      console.error('认证检查失败:', error)
      // 清除无效的认证信息
      await logout()
      return false
    }
  }

  const updateUserInfo = (newUserInfo) => {
    user.value = { ...user.value, ...newUserInfo }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  const refreshToken = async () => {
    try {
      // 这里可以实现token刷新逻辑
      // const response = await api.post('/auth/refresh')
      // const { token: newToken } = response.data
      // token.value = newToken
      // localStorage.setItem('token', newToken)
      // api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout()
      return false
    }
  }

  // 初始化时检查认证状态
  const initAuth = async () => {
    await checkAuth()
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 动作
    login,
    register,
    logout,
    checkAuth,
    updateUserInfo,
    refreshToken,
    initAuth
  }
})
</file>

<file path="frontend/src/utils/api.js">
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 显示加载状态
    if (config.showLoading !== false) {
      // 这里可以显示全局loading
    }

    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    
    return config
  },
  (error) => {
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 隐藏加载状态
    if (response.config.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    console.log('收到响应:', response.status, response.config.url, response.data)
    
    return response
  },
  async (error) => {
    // 隐藏加载状态
    if (error.config?.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    const { response, config } = error

    // 网络错误
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = response

    // 根据状态码处理不同错误
    switch (status) {
      case 400:
        ElMessage.error(data?.detail || '请求参数错误')
        break
        
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        delete api.defaults.headers.common['Authorization']
        
        ElMessageBox.confirm(
          '登录状态已过期，请重新登录',
          '提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          window.location.href = '/login'
        }).catch(() => {
          // 用户取消
        })
        break
        
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 验证错误
        if (data?.detail && Array.isArray(data.detail)) {
          const errors = data.detail.map(err => err.msg).join(', ')
          ElMessage.error(`数据验证失败: ${errors}`)
        } else {
          ElMessage.error(data?.detail || '数据验证失败')
        }
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
        
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后重试')
        break
        
      default:
        ElMessage.error(data?.detail || `请求失败 (${status})`)
    }

    console.error('请求失败:', status, config?.url, data)
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 认证相关
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh')
  },

  // 人格档案相关
  personalities: {
    list: () => api.get('/personalities'),
    create: (data) => api.post('/personalities', data),
    get: (id) => api.get(`/personalities/${id}`),
    update: (id, data) => api.put(`/personalities/${id}`, data),
    delete: (id) => api.delete(`/personalities/${id}`)
  },

  // 对话相关
  chat: {
    start: (personalityId) => api.post(`/chat/start/${personalityId}`),
    respond: (data) => api.post('/chat/respond', data),
    getHistory: (conversationId) => api.get(`/chat/history/${conversationId}`),
    getConversations: (personalityId) => api.get(`/chat/conversations/${personalityId}`)
  },

  // 预测相关
  prediction: {
    predict: (data) => api.post('/predict', data),
    getHistory: (personalityId) => api.get(`/predictions/${personalityId}`)
  },

  // 验证相关
  validation: {
    validate: (data) => api.post('/validate', data),
    getReports: (personalityId) => api.get(`/validation/reports/${personalityId}`)
  },

  // 分析相关
  analytics: {
    getOverview: () => api.get('/analytics/overview'),
    getPersonalityStats: (personalityId) => api.get(`/analytics/personality/${personalityId}`),
    getProgressReport: (personalityId) => api.get(`/analytics/progress/${personalityId}`)
  },

  // 模拟相关 - 西牟拉胡协议
  simulation: {
    start: (personalityId, initialMessage = '你好') =>
      api.post(`/api/v1/simulation/start/${personalityId}`, {
        personality_id: personalityId,
        initial_message: initialMessage
      }),
    chat: (conversationId, userInput) =>
      api.post(`/api/v1/simulation/chat/${conversationId}`, { user_input: userInput }),
    getConversations: (personalityId) =>
      api.get(`/api/v1/simulation/conversations/${personalityId}`),
    getMessages: (conversationId) =>
      api.get(`/api/v1/simulation/messages/${conversationId}`)
  }
}

// 文件上传
export const uploadFile = (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 下载文件
export const downloadFile = (url, filename) => {
  return api.get(url, {
    responseType: 'blob'
  }).then(response => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

// 批量请求
export const batchRequest = (requests) => {
  return Promise.allSettled(requests.map(request => api(request)))
}

// 重试机制
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}

export default api
</file>

<file path="frontend/src/views/auth/Login.vue">
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">人格复刻系统</h1>
        <p class="login-subtitle">100% Personality Cloning System</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.isLoading"
            @click="handleLogin"
          >
            {{ authStore.isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>
          还没有账号？
          <router-link to="/register" class="register-link">
            立即注册
          </router-link>
        </p>
      </div>
      
      <!-- 演示账号 -->
      <div class="demo-accounts">
        <el-divider>演示账号</el-divider>
        <el-button
          size="small"
          type="info"
          plain
          @click="fillDemoAccount"
        >
          使用演示账号
        </el-button>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      // 登录成功，跳转到首页
      router.push('/')
    }
  } catch (error) {
    console.error('登录处理失败:', error)
  }
}

// 填充演示账号
const fillDemoAccount = () => {
  loginForm.username = 'demo'
  loginForm.password = 'demo123'
  ElMessage.info('已填充演示账号信息')
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link:hover {
  text-decoration: underline;
}

.demo-accounts {
  margin-top: 30px;
  text-align: center;
}

.demo-accounts :deep(.el-divider__text) {
  background-color: rgba(255, 255, 255, 0.95);
  color: #7f8c8d;
  font-size: 12px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .circle-1,
  .circle-2,
  .circle-3 {
    display: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .login-title {
    color: #ecf0f1;
  }
  
  .login-subtitle {
    color: #bdc3c7;
  }
}
</style>
</file>

<file path="frontend/src/views/auth/Register.vue">
<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1 class="register-title">创建账号</h1>
        <p class="register-subtitle">加入人格复刻系统</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleRegister"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-button"
            :loading="authStore.isLoading"
            @click="handleRegister"
          >
            {{ authStore.isLoading ? '注册中...' : '注册' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="register-footer">
        <p>
          已有账号？
          <router-link to="/login" class="login-link">
            立即登录
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const registerFormRef = ref()

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return
    
    const result = await authStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    })
    
    if (result.success) {
      router.push('/')
    }
  } catch (error) {
    console.error('注册处理失败:', error)
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.register-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.register-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.register-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}
</style>
</file>

<file path="frontend/src/views/chat/ChatInterface.vue">
<template>
  <div class="chat-interface">
    <!-- 人格选择器 -->
    <el-card v-if="!selectedPersonality" class="personality-selector">
      <template #header>
        <h3>选择要对话的人格档案</h3>
      </template>

      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="48"><User /></el-icon>
        <p>暂无人格档案</p>
        <el-button type="primary" @click="createPersonality">
          创建人格档案
        </el-button>
      </div>

      <div v-else class="personality-list">
        <div
          v-for="personality in personalities"
          :key="personality.personality_id"
          class="personality-item"
          @click="selectPersonality(personality)"
        >
          <div class="personality-info">
            <h4>{{ personality.target_name }}</h4>
            <p>{{ personality.description || '暂无描述' }}</p>
            <el-progress
              :percentage="Math.round(personality.completion_percentage || 0)"
              :stroke-width="4"
              :show-text="false"
            />
          </div>
          <el-button type="primary">开始对话</el-button>
        </div>
      </div>
    </el-card>

    <!-- 对话界面 -->
    <div v-else class="chat-container">
      <!-- 对话头部 -->
      <el-card class="chat-header">
        <div class="header-content">
          <div class="personality-info">
            <h3>{{ selectedPersonality.target_name }}</h3>
            <p>完成度: {{ Math.round(selectedPersonality.completion_percentage || 0) }}%</p>
          </div>
          <div class="actions">
            <el-button @click="selectedPersonality = null">
              <el-icon><ArrowLeft /></el-icon>
              返回选择
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 消息列表 -->
      <el-card class="chat-messages" v-loading="loading">
        <div class="messages-container" ref="messagesContainer">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message', message.sender]"
          >
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <div v-if="messages.length === 0" class="empty-messages">
            <el-icon size="48"><ChatDotRound /></el-icon>
            <p>开始与 {{ selectedPersonality.target_name }} 对话吧！</p>
          </div>
        </div>
      </el-card>

      <!-- 输入区域 -->
      <el-card class="chat-input">
        <div class="input-container">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <span class="tip">Ctrl + Enter 发送</span>
            <el-button
              type="primary"
              :loading="sending"
              :disabled="!userInput.trim()"
              @click="sendMessage"
            >
              发送
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, User, ArrowLeft } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const personalities = ref([])
const selectedPersonality = ref(null)
const messages = ref([])
const userInput = ref('')
const loading = ref(false)
const sending = ref(false)
const conversationId = ref(null)
const messagesContainer = ref(null)

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const selectPersonality = async (personality) => {
  selectedPersonality.value = personality
  await startConversation()
}

const startConversation = async () => {
  try {
    loading.value = true

    // 使用新的模拟API启动对话
    const response = await apiMethods.simulation.start(
      selectedPersonality.value.personality_id,
      '你好，很高兴认识你！'
    )

    conversationId.value = response.data.conversation_id

    // 添加用户的初始消息和AI的回复
    messages.value = [
      {
        id: Date.now(),
        sender: 'user',
        content: '你好，很高兴认识你！',
        timestamp: new Date()
      },
      {
        id: Date.now() + 1,
        sender: 'ai',
        content: response.data.ai_response,
        timestamp: new Date()
      }
    ]

    await scrollToBottom()
  } catch (error) {
    console.error('启动对话失败:', error)
    ElMessage.error('启动对话失败，请重试')
  } finally {
    loading.value = false
  }
}

const sendMessage = async () => {
  if (!userInput.value.trim() || sending.value) return

  const messageText = userInput.value.trim()
  userInput.value = ''

  // 添加用户消息
  messages.value.push({
    id: Date.now(),
    sender: 'user',
    content: messageText,
    timestamp: new Date()
  })

  await scrollToBottom()

  try {
    sending.value = true

    // 使用新的模拟API发送消息
    const response = await apiMethods.simulation.chat(
      conversationId.value,
      messageText
    )

    // 添加AI回复
    messages.value.push({
      id: Date.now() + 1,
      sender: 'ai',
      content: response.data.ai_response,
      timestamp: new Date()
    })

    await scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sending.value = false
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const loadPersonalities = async () => {
  try {
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []

    // 如果URL中有personalityId参数，自动选择
    const personalityId = route.params.personalityId
    if (personalityId) {
      const personality = personalities.value.find(p => p.personality_id === personalityId)
      if (personality) {
        await selectPersonality(personality)
      }
    }
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.chat-interface {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.personality-selector {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.personality-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.personality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personality-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.personality-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #409eff;
  color: white;
}

.message.ai .message-content {
  background: #f5f7fa;
  color: #303133;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 5px;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.chat-input {
  flex-shrink: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface {
    padding: 10px;
  }

  .message-content {
    max-width: 85%;
  }

  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
</file>

<file path="frontend/src/views/Dashboard.vue">
<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>欢迎使用人格复刻系统</h1>
      <p>通过AI技术实现100%的人格复刻与分析</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPersonalities }}</div>
              <div class="stat-label">人格档案</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalConversations }}</div>
              <div class="stat-label">对话会话</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPredictions }}</div>
              <div class="stat-label">预测分析</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.averageAccuracy }}%</div>
              <div class="stat-label">平均准确度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速开始</span>
            </div>
          </template>
          
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              @click="createPersonality"
            >
              <el-icon><Plus /></el-icon>
              创建人格档案
            </el-button>
            
            <el-button
              type="success"
              size="large"
              @click="startChat"
            >
              <el-icon><ChatDotRound /></el-icon>
              开始对话分析
            </el-button>
            
            <el-button
              type="warning"
              size="large"
              @click="runPrediction"
            >
              <el-icon><TrendCharts /></el-icon>
              运行预测
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          
          <div class="recent-activities">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
            
            <div v-if="recentActivities.length === 0" class="no-activities">
              暂无活动记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统状态 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button type="text" @click="refreshStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">API服务:</span>
              <el-tag :type="systemStatus.api ? 'success' : 'danger'">
                {{ systemStatus.api ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">数据库:</span>
              <el-tag :type="systemStatus.database ? 'success' : 'danger'">
                {{ systemStatus.database ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">AI模型:</span>
              <el-tag :type="systemStatus.aiModel ? 'success' : 'danger'">
                {{ systemStatus.aiModel ? '正常' : '异常' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, ChatDotRound, TrendCharts, CircleCheck, Plus, Refresh } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { apiMethods } from '../utils/api'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const stats = reactive({
  totalPersonalities: 0,
  totalConversations: 0,
  totalPredictions: 0,
  averageAccuracy: 0
})

const recentActivities = ref([])

const systemStatus = reactive({
  api: true,
  database: true,
  aiModel: true
})

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const startChat = () => {
  router.push('/chat')
}

const runPrediction = () => {
  router.push('/prediction')
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const refreshStatus = async () => {
  try {
    // 这里可以调用API检查系统状态
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const loadDashboardData = async () => {
  try {
    // 加载真实的统计数据
    const [personalitiesResponse, analyticsResponse] = await Promise.allSettled([
      apiMethods.personalities.list(),
      // 暂时使用模拟数据，后续实现analytics API
      Promise.resolve({ data: { totalConversations: 0, totalPredictions: 0, averageAccuracy: 0 } })
    ])

    // 处理人格档案数据
    if (personalitiesResponse.status === 'fulfilled') {
      const personalities = personalitiesResponse.value.data || []
      stats.totalPersonalities = personalities.length
    } else {
      stats.totalPersonalities = 0
    }

    // 处理分析数据
    if (analyticsResponse.status === 'fulfilled') {
      const analytics = analyticsResponse.value.data
      stats.totalConversations = analytics.totalConversations || 0
      stats.totalPredictions = analytics.totalPredictions || 0
      stats.averageAccuracy = analytics.averageAccuracy || 0
    }

    // 暂时使用模拟的活动数据，后续从API获取
    recentActivities.value = [
      {
        id: 1,
        title: '系统启动成功',
        time: new Date(),
        icon: 'User'
      }
    ]
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败')
    // 设置默认值
    stats.totalPersonalities = 0
    stats.totalConversations = 0
    stats.totalPredictions = 0
    stats.averageAccuracy = 0
    recentActivities.value = []
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h1 {
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-header p {
  font-size: 16px;
  color: #7f8c8d;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

.quick-actions {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons .el-button {
  justify-content: flex-start;
  height: 50px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #667eea;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

.no-activities {
  text-align: center;
  color: #7f8c8d;
  padding: 40px 0;
}

.system-status {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-weight: 500;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .system-status {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
</file>

<file path="frontend/src/views/error/NotFound.vue">
<template>
  <div class="not-found">
    <div class="error-content">
      <div class="error-code">404</div>
      <h1 class="error-title">页面未找到</h1>
      <p class="error-description">
        抱歉，您访问的页面不存在或已被移除。
      </p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  text-align: center;
  color: white;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 20px;
  opacity: 0.8;
}

.error-title {
  font-size: 32px;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityCreate.vue">
<template>
  <div class="personality-create">
    <div class="page-header">
      <h1>创建人格档案</h1>
      <p>通过AI分析创建详细的人格档案</p>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="目标人物姓名" prop="target_name">
          <el-input
            v-model="form.target_name"
            placeholder="请输入要分析的人物姓名"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="人物描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请简要描述这个人的基本信息、性格特点、兴趣爱好等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Plus /></el-icon>
            创建档案
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  target_name: '',
  description: ''
})

// 验证规则
const rules = {
  target_name: [
    { required: true, message: '请输入目标人物姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入人物描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const response = await apiMethods.post('/personalities', form)

    ElMessage.success('人格档案创建成功！')
    router.push('/personalities')

  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error(error.response?.data?.detail || '创建失败，请重试')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/personalities')
}
</script>

<style scoped>
.personality-create {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityDetail.vue">
<template>
  <div class="personality-detail" v-loading="loading">
    <div v-if="personality">
      <el-page-header @back="goBack" class="page-header">
        <template #content>
          <span class="text-large font-600 mr-3">{{ personality.target_name }}</span>
        </template>
      </el-page-header>

      <el-card class="description-card">
        <p>{{ personality.description || '暂无详细描述。' }}</p>
      </el-card>

      <el-row :gutter="20">
        <!-- 左侧：人格特质 -->
        <el-col :xs="24" :md="10">
          <el-card>
            <template #header>
              <h3>人格特质</h3>
            </template>
            <!-- 大五人格雷达图 -->
            <div ref="radarChart" style="width: 100%; height: 300px;"></div>
            <!-- 其他核心特质 -->
            <el-descriptions :column="1" border>
              <el-descriptions-item label="依恋类型">
                <el-tag>{{ personality.attachment_style || '未知' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文化背景">
                {{ formatCulturalBackground(personality.cultural_background) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- 右侧：人生故事 -->
        <el-col :xs="24" :md="14">
          <el-card>
            <template #header>
              <h3>人生故事</h3>
            </template>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="关键事件" name="events">
                <el-timeline v-if="personality.events.length > 0">
                  <el-timeline-item
                    v-for="(event, index) in personality.events"
                    :key="index"
                    :timestamp="`年龄: ${event.age}`"
                    placement="top"
                  >
                    <el-card>
                      <h4>{{ event.title }}</h4>
                      <p>{{ event.narrative }}</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
                <el-empty v-else description="暂无关键事件记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="核心信念" name="beliefs">
                <el-collapse v-if="personality.beliefs.length > 0">
                  <el-collapse-item
                    v-for="(belief, index) in personality.beliefs"
                    :key="index"
                    :title="belief.statement"
                  >
                    <div>{{ belief.explanation }}</div>
                  </el-collapse-item>
                </el-collapse>
                <el-empty v-else description="暂无核心信念记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="家庭关系" name="family">
                 <div v-if="personality.family_members.length > 0">
                    <div v-for="(member, index) in personality.family_members" :key="index" class="family-member">
                       <strong>{{ member.relationship }}:</strong>
                       <span>{{ JSON.stringify(member.summary) }}</span>
                    </div>
                 </div>
                <el-empty v-else description="暂无家庭关系记录"></el-empty>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-empty v-else-if="!loading" description="未找到该人格档案"></el-empty>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import api from '../../utils/api'; // 使用默认导出的axios实例

echarts.use([TitleComponent, TooltipComponent, LegendComponent, RadarChart, CanvasRenderer]);

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const personality = ref(null);
const activeTab = ref('events');
const radarChart = ref(null);
let myChart = null;

const goBack = () => {
  router.push('/personalities');
};

const formatCulturalBackground = (bg) => {
  if (!bg) return '未知';
  return `${bg.region || ''} ${bg.generation || ''} ${bg.ethnicity || ''}`.trim();
};

const setupRadarChart = () => {
  if (radarChart.value && personality.value) {
    myChart = echarts.init(radarChart.value);
    const bigFive = personality.value.big_five;
    const option = {
      tooltip: {},
      radar: {
        indicator: [
          { name: '开放性 (O)', max: 1 },
          { name: '尽责性 (C)', max: 1 },
          { name: '外向性 (E)', max: 1 },
          { name: '宜人性 (A)', max: 1 },
          { name: '神经质 (N)', max: 1 },
        ],
      },
      series: [
        {
          name: '大五人格',
          type: 'radar',
          data: [
            {
              value: [
                bigFive.openness,
                bigFive.conscientiousness,
                bigFive.extraversion,
                bigFive.agreeableness,
                bigFive.neuroticism,
              ],
              name: personality.value.target_name,
            },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }
};

onMounted(async () => {
  const personalityId = route.params.id;
  if (!personalityId) {
    ElMessage.error('无效的人格档案ID');
    loading.value = false;
    return;
  }
  try {
    const response = await api.get(`/personalities/${personalityId}`);
    personality.value = response.data;
    await nextTick();
    setupRadarChart();
  } catch (error) {
    console.error('加载人格详情失败:', error);
    ElMessage.error('加载详情失败');
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.personality-detail {
  padding: 20px;
}
.page-header {
  margin-bottom: 20px;
}
.description-card {
  margin-bottom: 20px;
}
.family-member {
  margin-bottom: 10px;
  font-size: 14px;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityList.vue">
<template>
  <div class="personality-list">
    <div class="page-header">
      <h1>人格档案</h1>
      <el-button type="primary" @click="createPersonality">
        <el-icon><Plus /></el-icon>
        创建新档案
      </el-button>
    </div>

    <el-card v-loading="loading">
      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="64"><User /></el-icon>
        <h3>暂无人格档案</h3>
        <p>点击上方按钮创建您的第一个人格档案</p>
        <el-button type="primary" @click="createPersonality">
          <el-icon><Plus /></el-icon>
          立即创建
        </el-button>
      </div>

      <div v-else class="personality-grid">
        <div
          v-for="personality in personalities"
          :key="personality.profile_id"
          class="personality-card"
          @click="viewPersonality(personality.profile_id)"
        >
          <div class="card-header">
            <h3>{{ personality.target_name }}</h3>
            <el-tag :type="getStatusType(personality.completion_percentage)">
              {{ getStatusText(personality.completion_percentage) }}
            </el-tag>
          </div>

          <div class="card-content">
            <p class="description">{{ personality.description || '暂无描述' }}</p>

            <div class="progress-section">
              <div class="progress-label">
                <span>完成度</span>
                <span>{{ Math.round(personality.completion_percentage || 0) }}%</span>
              </div>
              <el-progress
                :percentage="Math.round(personality.completion_percentage || 0)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
          </div>

          <div class="card-footer">
            <span class="create-time">
              创建于 {{ formatDate(personality.created_at) }}
            </span>
            <div class="actions">
              <el-button
                size="small"
                type="primary"
                @click.stop="startChat(personality.profile_id)"
              >
                开始对话
              </el-button>
              <el-button
                size="small"
                @click.stop="viewPersonality(personality.profile_id)"
              >
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, User } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const loading = ref(false)
const personalities = ref([])

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const viewPersonality = (id) => {
  router.push(`/personalities/${id}`)
}

const startChat = (personalityId) => {
  router.push(`/chat/${personalityId}`)
}

const getStatusType = (percentage) => {
  if (percentage >= 80) return 'success'
  if (percentage >= 50) return 'warning'
  return 'info'
}

const getStatusText = (percentage) => {
  if (percentage >= 80) return '已完成'
  if (percentage >= 50) return '进行中'
  return '刚开始'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadPersonalities = async () => {
  try {
    loading.value = true
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
    personalities.value = []
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.personality-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state h3 {
  margin: 20px 0 10px;
  color: #606266;
}

.personality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.personality-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.card-content {
  margin-bottom: 15px;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.progress-section {
  margin-bottom: 10px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.actions {
  display: flex;
  gap: 8px;
}
</style>
</file>

<file path="frontend/vite.config.js">
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          charts: ['echarts', 'vue-echarts', 'd3'],
        },
      },
    },
  },
})
</file>

<file path="LICENSE">
MIT License

Copyright (c) 2024 100% Personality Cloning System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

IMPORTANT NOTICE:

This software is designed for research and educational purposes. Users are 
responsible for ensuring compliance with applicable laws, regulations, and 
ethical guidelines when using this system for personality analysis and modeling.

The developers of this software do not assume any responsibility for the 
misuse of this technology or any consequences arising from its use.

Please use this technology responsibly and ethically.
</file>

<file path="start_optimized.py">
#!/usr/bin/env python3
"""
优化后的系统启动脚本
专注于AI角色对话功能
"""

import os
import sys
import subprocess
import time
import asyncio
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("🎭" + "="*60 + "🎭")
    print("    100% 人格复刻系统 - 优化版")
    print("    专注于AI角色对话功能")
    print("🎭" + "="*60 + "🎭")

def check_requirements():
    """检查系统要求"""
    print("\n🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 10):
        print("❌ Python版本需要3.10或更高")
        return False
    print("✅ Python版本检查通过")
    
    # 检查Docker
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✅ Docker检查通过")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker未安装或无法访问")
        return False
    
    # 检查环境变量
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("⚠️  未找到.env文件，将使用默认配置")
    else:
        print("✅ 环境配置文件存在")
    
    return True

def start_database():
    """启动数据库服务"""
    print("\n🗄️  启动数据库服务...")
    
    try:
        # 启动PostgreSQL
        subprocess.run([
            'docker-compose', 'up', '-d', 'postgres'
        ], check=True)
        
        print("✅ PostgreSQL启动成功")
        
        # 等待数据库就绪
        print("⏳ 等待数据库就绪...")
        time.sleep(5)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库启动失败: {e}")
        return False

def install_dependencies():
    """安装Python依赖"""
    print("\n📦 安装Python依赖...")
    
    try:
        # 切换到backend目录
        os.chdir("backend")
        
        # 检查虚拟环境
        venv_path = Path("venv")
        if not venv_path.exists():
            print("🔧 创建虚拟环境...")
            subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        
        # 激活虚拟环境并安装依赖
        if os.name == 'nt':  # Windows
            pip_path = venv_path / "Scripts" / "pip"
        else:  # Unix/Linux/macOS
            pip_path = venv_path / "bin" / "pip"
        
        print("📥 安装依赖包...")
        subprocess.run([
            str(pip_path), 'install', '-r', 'requirements.txt'
        ], check=True)
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    finally:
        os.chdir("..")

def init_database():
    """初始化数据库"""
    print("\n🏗️  初始化数据库...")
    
    try:
        os.chdir("backend")
        
        # 运行数据库初始化
        if os.name == 'nt':  # Windows
            python_path = Path("venv") / "Scripts" / "python"
        else:  # Unix/Linux/macOS
            python_path = Path("venv") / "bin" / "python"
        
        subprocess.run([
            str(python_path), 'init_db.py'
        ], check=True)
        
        print("✅ 数据库初始化完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    finally:
        os.chdir("..")

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    
    try:
        os.chdir("backend")
        
        if os.name == 'nt':  # Windows
            python_path = Path("venv") / "Scripts" / "python"
        else:  # Unix/Linux/macOS
            python_path = Path("venv") / "bin" / "python"
        
        print("🌐 后端服务启动中...")
        print("📍 API地址: http://localhost:8000")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 启动FastAPI服务
        subprocess.run([
            str(python_path), '-m', 'uvicorn', 'main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ])
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断服务")
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
    finally:
        os.chdir("..")

def start_frontend():
    """启动前端服务（可选）"""
    print("\n🎨 启动前端服务...")
    
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("⚠️  前端目录不存在，跳过前端启动")
        return False
    
    try:
        os.chdir("frontend")
        
        # 检查node_modules
        if not Path("node_modules").exists():
            print("📦 安装前端依赖...")
            subprocess.run(['npm', 'install'], check=True)
        
        print("🌐 前端服务启动中...")
        print("📍 前端地址: http://localhost:5173")
        
        # 启动Vite开发服务器
        subprocess.run(['npm', 'run', 'dev'])
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断服务")
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
    finally:
        os.chdir("..")

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 使用指南:")
    print("=" * 50)
    print("1. 🎭 生成角色数据:")
    print("   cd backend && python character_generator.py")
    print()
    print("2. 🔄 批量生成角色:")
    print("   cd backend && python batch_character_generator.py")
    print()
    print("3. 🛠️  管理角色:")
    print("   cd backend && python character_manager.py")
    print()
    print("4. 🌐 API文档:")
    print("   http://localhost:8000/docs")
    print()
    print("5. 💬 AI角色对话:")
    print("   使用 /api/v1/simulation/* 端点")
    print("=" * 50)

def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请解决上述问题后重试")
        return 1
    
    # 启动数据库
    if not start_database():
        print("\n❌ 数据库启动失败")
        return 1
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        return 1
    
    # 初始化数据库
    if not init_database():
        print("\n❌ 数据库初始化失败")
        return 1
    
    print("\n✅ 系统初始化完成!")
    
    # 显示使用指南
    show_usage_guide()
    
    # 询问是否启动服务
    print("\n🚀 是否启动后端服务? (y/N): ", end="")
    choice = input().strip().lower()
    
    if choice == 'y':
        start_backend()
    else:
        print("\n✅ 系统准备就绪!")
        print("💡 提示: 运行 'python start_optimized.py' 并选择 'y' 来启动后端服务")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  启动过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        sys.exit(1)
</file>

<file path="test_fixes.py">
#!/usr/bin/env python3
"""
测试修复后的系统核心功能
验证JWT认证、角色生成和AI对话是否正常工作
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_jwt_functions():
    """测试JWT功能"""
    print("🔐 测试JWT认证功能...")
    
    try:
        from main import create_access_token, SECRET_KEY, ALGORITHM
        from jose import jwt
        from datetime import timedelta
        
        # 测试创建token
        test_data = {"sub": "demo", "user_id": "test-id"}
        token = create_access_token(test_data, timedelta(minutes=30))
        
        # 测试解析token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        if payload.get("sub") == "demo":
            print("✅ JWT认证功能正常")
            return True
        else:
            print("❌ JWT解析失败")
            return False
            
    except Exception as e:
        print(f"❌ JWT测试失败: {e}")
        return False

async def test_character_generator():
    """测试角色生成器"""
    print("\n🎭 测试角色生成器...")
    
    try:
        # 检查instructor是否可用
        import instructor
        import google.generativeai as genai
        
        # 检查API密钥
        from dotenv import load_dotenv
        load_dotenv("backend/.env")
        
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
            print("⚠️  Gemini API Key未配置，跳过角色生成测试")
            return False
        
        print("✅ 角色生成器依赖检查通过")
        print("💡 提示: 运行 'python backend/character_generator.py' 进行完整测试")
        return True
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False
    except Exception as e:
        print(f"❌ 角色生成器测试失败: {e}")
        return False

async def test_personality_simulator():
    """测试人格模拟器"""
    print("\n🤖 测试人格模拟器...")
    
    try:
        from app.services.personality_simulator import PersonalitySimulator
        
        simulator = PersonalitySimulator()
        
        if simulator.available:
            print("✅ 人格模拟器初始化成功")
            return True
        else:
            print("⚠️  人格模拟器初始化成功，但LLM不可用")
            print("💡 请检查GEMINI_API_KEY环境变量")
            return False
            
    except Exception as e:
        print(f"❌ 人格模拟器测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "backend/main.py",
        "backend/character_generator.py",
        "backend/batch_character_generator.py",
        "backend/character_manager.py",
        "backend/app/services/personality_simulator.py",
        "backend/requirements.txt",
        "docker-compose.yml",
        "start_optimized.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ 核心文件结构完整")
        return True
    else:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("\n📦 测试Python依赖...")
    
    required_packages = [
        "fastapi",
        "sqlalchemy",
        "pydantic", 
        "google-generativeai",
        "instructor",
        "python-jose",
        "structlog"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if not missing_packages:
        print("✅ 核心依赖项完整")
        return True
    else:
        print("❌ 缺少以下依赖:")
        for package in missing_packages:
            print(f"   - {package}")
        print("💡 运行: pip install -r backend/requirements.txt")
        return False

async def main():
    """主函数"""
    print("🧪 系统修复验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 文件结构测试
    test_results.append(("文件结构", test_file_structure()))
    
    # 2. 依赖项测试
    test_results.append(("依赖项", test_dependencies()))
    
    # 3. JWT功能测试
    test_results.append(("JWT认证", await test_jwt_functions()))
    
    # 4. 人格模拟器测试
    test_results.append(("人格模拟器", await test_personality_simulator()))
    
    # 5. 角色生成器测试
    test_results.append(("角色生成器", await test_character_generator()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有修复验证通过！系统可以正常运行")
        print("\n📚 下一步操作:")
        print("1. 启动系统: python start_optimized.py")
        print("2. 生成角色: cd backend && python batch_character_generator.py")
        print("3. 测试对话: cd backend && python character_manager.py")
        return True
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，请先解决上述问题")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
</file>

</files>
