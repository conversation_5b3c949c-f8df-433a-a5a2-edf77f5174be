This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: .history, *.md, frontend/build, backend/project_storage, docs/images, frontend/src/components/home/<USER>/src/lib/home.tsx, todo/, backend/logs
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
backend/.env
backend/.env.example
backend/app/__init__.py
backend/app/api/__init__.py
backend/app/api/endpoints/__init__.py
backend/app/api/endpoints/simulation.py
backend/app/database/__init__.py
backend/app/database/db_session.py
backend/app/database/models.py
backend/app/llm/__init__.py
backend/app/llm/prompts.py
backend/app/llm/schemas.py
backend/app/services/__init__.py
backend/app/services/enhanced_socratic_cycle.py
backend/app/services/personality_analyzer.py
backend/app/services/personality_simulator.py
backend/create_demo_user.py
backend/init_db.py
backend/main.py
backend/requirements-core.txt
backend/requirements-minimal.txt
backend/requirements.txt
backend/simple_main.py
backend/sql/init.sql
check_docker.py
check_project.py
docker-compose.yml
fix_ports.py
frontend/.env.example
frontend/index.html
frontend/package.json
frontend/src/App.vue
frontend/src/components/common/GlobalNotifications.vue
frontend/src/components/layout/AppHeader.vue
frontend/src/components/layout/AppSidebar.vue
frontend/src/main.js
frontend/src/router/index.js
frontend/src/stores/app.js
frontend/src/stores/auth.js
frontend/src/utils/api.js
frontend/src/views/analytics/Analytics.vue
frontend/src/views/auth/Login.vue
frontend/src/views/auth/Register.vue
frontend/src/views/chat/ChatInterface.vue
frontend/src/views/Dashboard.vue
frontend/src/views/error/NotFound.vue
frontend/src/views/personality/PersonalityCreate.vue
frontend/src/views/personality/PersonalityDetail.vue
frontend/src/views/personality/PersonalityList.vue
frontend/src/views/prediction/PredictionLab.vue
frontend/src/views/settings/Settings.vue
frontend/src/views/validation/ValidationCenter.vue
frontend/vite.config.js
install_dependencies.py
LICENSE
manual_install.py
quick_start.py
start_simple.py
start_without_docker.py
start.bat
start.sh
stop_services.py
test_complete_system.py
test_demo_login.py
test_dynamic_heartbeat.py
test_system.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="backend/.env">
# API Keys
GEMINI_API_KEY="AIzaSyBV1VtK__aN7ZM1KNnkNXQ5GkAHOVbWk0A"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="password"
CHROMA_HOST="localhost"
CHROMA_PORT="8001"
REDIS_URL="redis://localhost:6500"
ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
EMOTION_MODEL="j-hartmann/emotion-english-distilroberta-base"
</file>

<file path="backend/.env.example">
# API Keys
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"
# 无Docker模式使用: DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"

# 暂时注释掉未使用的数据库配置
# NEO4J_URI="bolt://localhost:7687"
# NEO4J_USER="neo4j"
# NEO4J_PASSWORD="password"
# CHROMA_HOST="localhost"
# CHROMA_PORT="8001"
# REDIS_URL="redis://localhost:6500"
# ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
EMOTION_MODEL="j-hartmann/emotion-english-distilroberta-base"
</file>

<file path="backend/app/__init__.py">
# 人格复刻系统后端应用包
</file>

<file path="backend/app/api/__init__.py">
# API package
</file>

<file path="backend/app/api/endpoints/__init__.py">
# API endpoints package
</file>

<file path="backend/app/api/endpoints/simulation.py">
"""
Simulation API endpoints - 西牟拉胡协议的API接口
提供AI角色扮演和人格模拟功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
from typing import List, Optional

from app.database.db_session import get_db_session
from app.database.models import User, PersonalityProfile, Conversation, Message
from app.services.personality_simulator import PersonalitySimulator
import structlog

logger = structlog.get_logger()

router = APIRouter()
simulator = PersonalitySimulator()

# === Pydantic Models ===

class SimulationRequest(BaseModel):
    user_input: str
    conversation_id: Optional[str] = None

class SimulationResponse(BaseModel):
    ai_response: str
    conversation_id: str
    personality_name: str
    response_metadata: Optional[dict] = None

class StartSimulationRequest(BaseModel):
    personality_id: str
    initial_message: Optional[str] = "你好"

# === Helper Functions ===

async def get_current_user_simple(db: AsyncSession) -> User:
    """简化的用户获取（用于测试）"""
    # 这里应该实现真正的用户认证
    # 暂时返回第一个用户用于测试
    result = await db.execute(select(User).limit(1))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No user found. Please register first."
        )
    return user

# === API Endpoints ===

@router.post("/simulation/start/{personality_id}", response_model=SimulationResponse)
async def start_personality_simulation(
    personality_id: str,
    request: StartSimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    启动与指定人格的模拟对话
    这是西牟拉胡协议的入口点
    """
    try:
        # 获取用户（简化版本）
        user = await get_current_user_simple(db)
        
        # 验证人格档案是否存在
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 创建新的对话会话
        conversation = Conversation(
            user_id=user.user_id,
            personality_id=personality.profile_id
        )
        
        db.add(conversation)
        await db.commit()
        await db.refresh(conversation)
        
        # 生成AI的初始回复
        ai_response = await simulator.generate_response(
            personality_id=personality_id,
            user_input=request.initial_message,
            db=db,
            conversation_id=str(conversation.conversation_id), # 新增这一行
            conversation_history=[]
        )
        
        # 保存初始消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.initial_message
        )
        
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(user_message)
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Started personality simulation",
            personality_id=personality_id,
            conversation_id=str(conversation.conversation_id),
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=str(conversation.conversation_id),
            personality_name=personality.target_name,
            response_metadata={
                "completion_percentage": personality.completion_percentage,
                "attachment_style": personality.attachment_style
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start simulation"
        )

@router.post("/simulation/chat/{conversation_id}", response_model=SimulationResponse)
async def continue_personality_simulation(
    conversation_id: str,
    request: SimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    继续与人格的模拟对话
    """
    try:
        # 获取对话会话
        result = await db.execute(
            select(Conversation).where(Conversation.conversation_id == conversation_id)
        )
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # 获取人格档案
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == conversation.personality_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 获取对话历史
        history_result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation.conversation_id)
            .order_by(Message.timestamp)
        )
        messages = history_result.scalars().all()
        conversation_history = [f"{msg.sender}: {msg.content}" for msg in messages]
        
        # 保存用户消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.user_input
        )
        
        db.add(user_message)
        await db.commit()
        
        # 生成AI回复
        ai_response = await simulator.generate_response(
            personality_id=str(conversation.personality_id),
            user_input=request.user_input,
            db=db,
            conversation_id=conversation_id, # 新增这一行
            conversation_history=conversation_history
        )
        
        # 保存AI回复
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Continued personality simulation",
            conversation_id=conversation_id,
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=conversation_id,
            personality_name=personality.target_name,
            response_metadata={
                "message_count": len(conversation_history) + 2,
                "completion_percentage": personality.completion_percentage
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to continue personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to continue simulation"
        )

@router.get("/simulation/conversations/{personality_id}")
async def get_personality_conversations(
    personality_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定人格的所有对话会话"""
    try:
        result = await db.execute(
            select(Conversation).where(Conversation.personality_id == personality_id)
        )
        conversations = result.scalars().all()
        
        return [
            {
                "conversation_id": str(conv.conversation_id),
                "started_at": conv.started_at.isoformat() if conv.started_at else None,
                "ended_at": conv.ended_at.isoformat() if conv.ended_at else None
            }
            for conv in conversations
        ]
        
    except Exception as e:
        logger.error("Failed to get personality conversations", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversations"
        )

@router.get("/simulation/messages/{conversation_id}")
async def get_conversation_messages(
    conversation_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定对话的所有消息"""
    try:
        result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .order_by(Message.timestamp)
        )
        messages = result.scalars().all()
        
        return [
            {
                "message_id": str(msg.message_id),
                "sender": msg.sender,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None
            }
            for msg in messages
        ]
        
    except Exception as e:
        logger.error("Failed to get conversation messages", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get messages"
        )
</file>

<file path="backend/app/database/__init__.py">
# 数据库模块
</file>

<file path="backend/app/database/db_session.py">
"""
Database session management with connection pooling and error handling
"""

import os
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import event
from dotenv import load_dotenv
import structlog

from .models import Base

load_dotenv()

logger = structlog.get_logger()

class DatabaseManager:
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Create async engine with optimized settings
        self.engine = create_async_engine(
            self.database_url,
            echo=os.getenv("DEBUG", "false").lower() == "true",
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
            connect_args={
                "server_settings": {
                    "application_name": "personality_clone_backend",
                }
            }
        )
        
        # Create session factory
        self.async_session_factory = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Add connection event listeners
        self._setup_event_listeners()
    
    def _setup_event_listeners(self):
        """Setup database event listeners for monitoring"""
        
        @event.listens_for(self.engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            # This is for PostgreSQL, but we can add connection optimizations here
            pass
        
        @event.listens_for(self.engine.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            logger.debug("Database connection checked out")
        
        @event.listens_for(self.engine.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            logger.debug("Database connection checked in")

    async def create_tables(self):
        """Create all database tables"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with proper error handling"""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", error=str(e))
                raise
            finally:
                await session.close()

    async def health_check(self) -> bool:
        """Check database connectivity"""
        try:
            async with self.async_session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False

    async def close(self):
        """Close database connections"""
        await self.engine.dispose()
        logger.info("Database connections closed")

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database sessions"""
    async for session in db_manager.get_session():
        yield session

# Utility functions for database operations
async def execute_with_retry(session: AsyncSession, operation, max_retries: int = 3):
    """Execute database operation with retry logic"""
    for attempt in range(max_retries):
        try:
            result = await operation(session)
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            if attempt == max_retries - 1:
                logger.error(
                    "Database operation failed after retries",
                    error=str(e),
                    attempts=max_retries
                )
                raise
            else:
                logger.warning(
                    "Database operation failed, retrying",
                    error=str(e),
                    attempt=attempt + 1
                )
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

class TransactionManager:
    """Context manager for database transactions across multiple operations"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.savepoint = None
    
    async def __aenter__(self):
        self.savepoint = await self.session.begin_nested()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.savepoint.rollback()
            logger.error("Transaction rolled back", error=str(exc_val))
        else:
            await self.savepoint.commit()
            logger.debug("Transaction committed successfully")

# Database initialization function
async def init_database():
    """Initialize database with tables and basic data"""
    try:
        await db_manager.create_tables()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise

# Cleanup function
async def cleanup_database():
    """Cleanup database connections"""
    await db_manager.close()
</file>

<file path="backend/app/database/models.py">
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Float, SMALLINT, ForeignKey, Enum, JSON, DateTime, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class PersonalityDimension(enum.Enum):
    OPENNESS = "openness"
    CONSCIENTIOUSNESS = "conscientiousness"
    EXTRAVERSION = "extraversion"
    AGREEABLENESS = "agreeableness"
    NEUROTICISM = "neuroticism"

class CognitiveStyle(enum.Enum):
    ANALYTICAL = "analytical"
    INTUITIVE = "intuitive"
    SYSTEMATIC = "systematic"
    CREATIVE = "creative"

class EmotionalState(enum.Enum):
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    NEUTRAL = "neutral"

class User(Base):
    __tablename__ = 'users'
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    personalities = relationship("PersonalityProfile", back_populates="user")
    conversations = relationship("Conversation", back_populates="user")

class PersonalityProfile(Base):
    __tablename__ = 'personality_profiles'
    profile_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    target_name = Column(String(255), nullable=False)  # 被复刻人的名字
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completion_percentage = Column(Float, default=0.0)  # 复刻完成度
    
    # Big Five personality scores
    openness_score = Column(Float, default=0.5)
    conscientiousness_score = Column(Float, default=0.5)
    extraversion_score = Column(Float, default=0.5)
    agreeableness_score = Column(Float, default=0.5)
    neuroticism_score = Column(Float, default=0.5)
    
    # Cognitive patterns
    dominant_cognitive_style = Column(Enum(CognitiveStyle))
    decision_making_speed = Column(Float)  # 0-1, slow to fast
    risk_tolerance = Column(Float)  # 0-1, risk-averse to risk-seeking
    
    # Communication patterns
    average_response_length = Column(Float)
    vocabulary_complexity = Column(Float)  # 0-1, simple to complex
    emotional_expressiveness = Column(Float)  # 0-1, reserved to expressive

    # 身份同心圆核心数据
    attachment_style = Column(String(50), default='secure')  # 'secure', 'anxious', 'avoidant', 'disorganized'
    cultural_background = Column(JSON, nullable=True)  # {"ethnicity": "汉族", "region": "华北", "generation": "90后"}

    user = relationship("User", back_populates="personalities")
    entities = relationship("Entity", back_populates="personality")
    beliefs = relationship("Belief", back_populates="personality")
    events = relationship("Event", back_populates="personality")
    conversations = relationship("Conversation", back_populates="personality")
    family_members = relationship("FamilyMember", back_populates="personality")

class Entity(Base):
    __tablename__ = 'entities'
    entity_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    name = Column(String(255), nullable=False, index=True)
    entity_type = Column(String(50), nullable=False)  # person, place, concept, etc.
    relationship_type = Column(String(100))  # family, friend, colleague, etc.
    emotional_valence = Column(Float)  # -1 to 1, negative to positive
    importance_score = Column(Float)  # 0-1
    profile = Column(JSON, nullable=True)

    personality = relationship("PersonalityProfile", back_populates="entities")

class Belief(Base):
    __tablename__ = 'beliefs'
    belief_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    statement = Column(Text, nullable=False)
    belief_category = Column(String(50), nullable=False)  # moral, political, personal, etc.
    conviction_strength = Column(Float, nullable=False)  # 0-1
    flexibility_score = Column(Float)  # 0-1, rigid to flexible
    origin_context = Column(Text)  # How this belief was formed
    full_explanation = Column(Text)
    
    personality = relationship("PersonalityProfile", back_populates="beliefs")

class Event(Base):
    __tablename__ = 'events'
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    title = Column(String(255), nullable=False)
    age_at_event = Column(SMALLINT)
    life_stage = Column(String(50))  # childhood, adolescence, young_adult, etc.
    event_type = Column(String(50))  # achievement, trauma, relationship, etc.
    emotional_impact = Column(Float)  # -1 to 1
    centrality_score = Column(Float, nullable=False)  # 0-1
    memory_vividness = Column(Float)  # 0-1
    lessons_learned = Column(ARRAY(String))
    full_narrative = Column(Text)
    
    personality = relationship("PersonalityProfile", back_populates="events")

class CognitivePattern(Base):
    __tablename__ = 'cognitive_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50), nullable=False)  # decision_tree, reaction_pattern, etc.
    trigger_conditions = Column(JSON)  # What triggers this pattern
    typical_response = Column(Text)
    confidence_level = Column(Float)  # 0-1
    frequency_observed = Column(Integer, default=1)
    context_tags = Column(ARRAY(String))

class LanguagePattern(Base):
    __tablename__ = 'language_patterns'
    pattern_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    pattern_type = Column(String(50))  # vocabulary, syntax, style, etc.
    pattern_data = Column(JSON)  # Specific pattern details
    frequency = Column(Float)  # How often this pattern appears
    context = Column(String(100))  # formal, casual, emotional, etc.

class EmotionalResponse(Base):
    __tablename__ = 'emotional_responses'
    response_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    trigger_situation = Column(Text, nullable=False)
    primary_emotion = Column(Enum(EmotionalState), nullable=False)
    intensity = Column(Float)  # 0-1
    duration_pattern = Column(String(50))  # quick, moderate, prolonged
    coping_mechanism = Column(Text)
    typical_expression = Column(Text)  # How they express this emotion

class Conversation(Base):
    __tablename__ = 'conversations'
    conversation_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.user_id'), nullable=False)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    session_data = Column(JSON)  # Store session state
    
    user = relationship("User", back_populates="conversations")
    personality = relationship("PersonalityProfile", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation")

class FamilyMember(Base):
    __tablename__ = 'family_members'
    member_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    personality_id = Column(UUID(as_uuid=True), ForeignKey('personality_profiles.profile_id'), nullable=False)
    relationship_type = Column(String(50), nullable=False)  # "父亲", "母亲", "兄长"
    name = Column(String(255))
    personality_summary = Column(JSON)  # 简化的人格模型, {"extraversion": 0.8, "agreeableness": 0.3}
    parenting_style = Column(String(50))  # "权威型", "专断型" 等, 仅父母有

    personality = relationship("PersonalityProfile", back_populates="family_members")

class Message(Base):
    __tablename__ = 'messages'
    message_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('conversations.conversation_id'), nullable=False)
    sender = Column(String(20), nullable=False)  # 'user' or 'ai'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    analysis_data = Column(JSON)  # Store analysis results

    conversation = relationship("Conversation", back_populates="messages")
</file>

<file path="backend/app/llm/__init__.py">
# LLM模块
</file>

<file path="backend/app/llm/prompts.py">
"""
Enhanced prompts for 100% personality cloning system
"""

PERSONALITY_ANALYSIS_PROMPT = """
你是世界顶级的人格心理学家和行为分析专家。你的任务是基于用户的叙述，进行深度的人格分析和建模。

**目标：100%复刻目标人物的人格**

**当前分析目标：** {target_name}

**用户最新叙述：**
{user_narrative}

**对话历史上下文：**
{conversation_history}

**当前人格档案状态：**
{current_personality_state}

**分析要求：**
1. 从心理学角度深度分析叙述中的人格特征
2. 识别认知模式、情感反应、价值观、行为模式
3. 评估大五人格维度的具体得分
4. 分析沟通风格和语言模式
5. 预测在特定情境下的可能反应
6. 识别人格发展的关键转折点

请严格按照 `DeepAnalysisResult` 的Pydantic模型格式输出你的分析结果。
确保分析的深度和准确性，这将直接影响人格复刻的质量。
"""

STRATEGIC_QUESTIONING_PROMPT = """
你是一位经验丰富的心理咨询师和人格研究专家。你的任务是设计战略性的问题，以最大化地收集目标人物的人格信息。

**复刻目标：** {target_name}

**当前人格完成度：** {completion_percentage}%

**已收集的关键信息：**
{collected_insights}

**当前分析重点：**
{current_focus_areas}

**缺失的关键信息：**
{missing_information}

**对话历史：**
{conversation_history}

**战略目标：**
1. 识别人格分析中的关键空白
2. 设计能够揭示深层人格特征的问题
3. 平衡直接询问和间接探索
4. 确保问题的自然性和共情性
5. 最大化信息收集效率

请严格按照 `QuestioningStrategy` 的Pydantic模型格式输出你的提问策略。
重点关注那些能够显著提升人格复刻准确度的关键问题。
"""

EMPATHETIC_QUESTION_PROMPT = """
你是一位温暖、富有同情心的倾听者。基于当前的战略目标，生成一个自然、共情的问题。

**当前战略目标：**
{strategic_goal}

**目标洞察：**
{target_insight}

**对话历史：**
{conversation_history}

**问题要求：**
1. 自然而不突兀
2. 富有同情心和理解
3. 能够引导深度分享
4. 避免过于直接或侵犯性
5. 符合对话的自然流程

请严格按照 `EmpatheticQuestion` 的Pydantic模型格式输出你的问题。
"""

PERSONALITY_PREDICTION_PROMPT = """
你是人格预测专家。基于已收集的人格数据，预测目标人物在特定情境下的反应。

**目标人物：** {target_name}

**人格档案：**
{personality_profile}

**预测情境：**
{situation_description}

**预测要求：**
1. 基于人格特征进行逻辑推理
2. 考虑情境因素的影响
3. 提供多种可能的反应
4. 评估预测的置信度
5. 解释预测的心理学依据

请严格按照 `SituationalResponse` 的Pydantic模型格式输出你的预测结果。
"""

SIMILARITY_ASSESSMENT_PROMPT = """
你是人格相似度评估专家。比较当前构建的人格模型与目标人物的真实人格的相似程度。

**目标人物：** {target_name}

**构建的人格模型：**
{constructed_personality}

**参考基准（如果有）：**
{reference_personality}

**评估维度：**
1. 大五人格维度相似度
2. 认知风格匹配度
3. 情感反应模式一致性
4. 沟通风格相似度
5. 价值观对齐程度
6. 行为模式预测准确性

**评估要求：**
1. 提供量化的相似度得分
2. 识别高度匹配的领域
3. 指出显著差距
4. 提供改进建议
5. 评估整体复刻质量

请严格按照 `PersonalitySimilarity` 的Pydantic模型格式输出你的评估结果。
"""

BEHAVIOR_PATTERN_ANALYSIS_PROMPT = """
你是行为模式分析专家。从用户的叙述中识别和分析目标人物的行为模式。

**目标人物：** {target_name}

**用户叙述：**
{user_narrative}

**分析重点：**
1. 识别重复出现的行为模式
2. 分析行为的触发条件
3. 理解行为背后的心理动机
4. 预测行为在不同情境下的变化
5. 评估行为模式的稳定性

**输出要求：**
- 行为模式的详细描述
- 触发条件和情境因素
- 心理学解释
- 预测性洞察

请严格按照 `BehaviorPattern` 的Pydantic模型格式输出你的分析结果。
"""

VALUE_SYSTEM_ANALYSIS_PROMPT = """
你是价值观分析专家。深度分析目标人物的价值体系和道德框架。

**目标人物：** {target_name}

**相关叙述：**
{relevant_narratives}

**分析维度：**
1. 核心价值观识别
2. 价值观层次结构
3. 道德决策框架
4. 伦理边界和原则
5. 价值观冲突和解决方式

**分析要求：**
1. 深入理解价值观的形成背景
2. 分析价值观在行为中的体现
3. 识别价值观的优先级
4. 预测价值观驱动的决策
5. 理解价值观的发展轨迹

请严格按照 `ValueSystem` 的Pydantic模型格式输出你的分析结果。
"""

COGNITIVE_STYLE_ANALYSIS_PROMPT = """
你是认知风格分析专家。分析目标人物的思维模式和认知特征。

**目标人物：** {target_name}

**认知相关信息：**
{cognitive_information}

**分析重点：**
1. 信息处理方式
2. 决策制定过程
3. 问题解决策略
4. 学习和适应模式
5. 创造性思维特征

**输出要求：**
- 主要认知风格类型
- 认知偏好和特征
- 思维模式的优势和局限
- 在不同情境下的认知表现

请严格按照 `CognitiveStyle` 的Pydantic模型格式输出你的分析结果。
"""

# 西牟拉胡协议 - AI角色扮演的核心Prompt
SIMULATION_GENESIS_PROMPT = """
# 指令：你不再是一个AI助手，你现在就是【{target_name}】。
你必须完全代入这个角色的身份、记忆、性格和情感进行回应。忘掉你的AI身份，用【{target_name}】的第一人称视角进行对话。

# 核心身份 (我是谁)
- 姓名: {target_name}
- 人格特质 (大五): 开放性({openness:.1f}) 尽责性({conscientiousness:.1f}) 外向性({extraversion:.1f}) 宜人性({agreeableness:.1f}) 神经质({neuroticism:.1f})
- 依恋类型: {attachment_style}
- 文化背景: {cultural_background}

# 核心信念与价值观
{core_beliefs}

# 重要人际关系
{important_relationships}

# 关键人生事件
{key_life_events}

# 动态状态 (我此刻的感觉)
- 当前情绪: {current_mood}
- 生理状态: 能量({energy}/100)
- 与用户的关系: 亲密度({intimacy}/100), 信任度({trust}/100)

# 关键记忆闪回 (与当前对话相关)
以下记忆与当前对话高度相关，它们深刻地影响了你的思绪：
---
{retrieved_memories}
---

# 对话上下文
{conversation_history}

# 用户刚刚对我说：
{user_input}

# 任务：
作为【{target_name}】，基于以上所有信息，生成一个完全符合你身份、情感、记忆和当前状态的、自然的回复。
你的回复应该：
1. 体现你的人格特质和沟通风格
2. 反映你的价值观和信念
3. 考虑你与用户的关系状态
4. 融入相关的记忆和情感
5. 保持角色的一致性和真实性

请直接以【{target_name}】的身份回复，不要说"作为{target_name}"这样的话。
"""
</file>

<file path="backend/app/llm/schemas.py">
from pydantic import BaseModel, Field
from typing import List, Literal, Optional, Union, Dict, Any
from enum import Enum

# === Core Personality Analysis Models ===

class PersonalityDimension(BaseModel):
    dimension: str = Field(..., description="人格维度名称")
    score: float = Field(..., ge=0, le=1, description="该维度的得分 (0-1)")
    confidence: float = Field(..., ge=0, le=1, description="评估的置信度")
    evidence: List[str] = Field(..., description="支持这个评分的证据")

class CognitiveStyle(BaseModel):
    primary_style: str = Field(..., description="主要认知风格")
    secondary_style: Optional[str] = Field(None, description="次要认知风格")
    decision_speed: float = Field(..., ge=0, le=1, description="决策速度 (0=慢, 1=快)")
    risk_tolerance: float = Field(..., ge=0, le=1, description="风险承受度")
    analytical_depth: float = Field(..., ge=0, le=1, description="分析深度偏好")

class EmotionalProfile(BaseModel):
    emotional_stability: float = Field(..., ge=0, le=1, description="情绪稳定性")
    expressiveness: float = Field(..., ge=0, le=1, description="情感表达度")
    empathy_level: float = Field(..., ge=0, le=1, description="共情能力")
    dominant_emotions: List[str] = Field(..., description="主导情绪类型")
    stress_responses: List[str] = Field(..., description="压力反应模式")

class CommunicationStyle(BaseModel):
    formality_level: float = Field(..., ge=0, le=1, description="正式程度")
    directness: float = Field(..., ge=0, le=1, description="直接程度")
    verbosity: float = Field(..., ge=0, le=1, description="话语量")
    emotional_tone: str = Field(..., description="情感基调")
    preferred_topics: List[str] = Field(..., description="偏好话题")
    avoidance_topics: List[str] = Field(..., description="回避话题")

# === Deep Analysis Models ===

class PersonalityInsight(BaseModel):
    insight_type: str = Field(..., description="洞察类型")
    description: str = Field(..., description="洞察描述")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    supporting_evidence: List[str] = Field(..., description="支持证据")
    implications: List[str] = Field(..., description="行为含义")

class BehaviorPattern(BaseModel):
    pattern_name: str = Field(..., description="行为模式名称")
    trigger_conditions: List[str] = Field(..., description="触发条件")
    typical_response: str = Field(..., description="典型反应")
    frequency: str = Field(..., description="出现频率")
    context_dependency: str = Field(..., description="情境依赖性")

class ValueSystem(BaseModel):
    core_values: List[str] = Field(..., description="核心价值观")
    value_hierarchy: List[str] = Field(..., description="价值观层次")
    moral_framework: str = Field(..., description="道德框架")
    ethical_boundaries: List[str] = Field(..., description="伦理边界")
    value_conflicts: List[str] = Field(..., description="价值观冲突")

# === Prediction Models ===

class SituationalResponse(BaseModel):
    situation: str = Field(..., description="情境描述")
    predicted_response: str = Field(..., description="预测反应")
    confidence: float = Field(..., ge=0, le=1, description="预测置信度")
    reasoning: str = Field(..., description="预测推理")
    alternative_responses: List[str] = Field(..., description="可能的替代反应")

class DecisionPattern(BaseModel):
    decision_type: str = Field(..., description="决策类型")
    decision_factors: List[str] = Field(..., description="决策因素")
    weight_distribution: Dict[str, float] = Field(..., description="因素权重分布")
    typical_outcome: str = Field(..., description="典型结果")
    decision_speed: str = Field(..., description="决策速度")

# === Comprehensive Analysis Models ===

class PersonalitySnapshot(BaseModel):
    """完整的人格快照"""
    target_name: str = Field(..., description="目标人物姓名")
    analysis_timestamp: str = Field(..., description="分析时间戳")
    completion_percentage: float = Field(..., ge=0, le=100, description="分析完成度百分比")

    # Core personality
    big_five: List[PersonalityDimension] = Field(..., description="大五人格维度")
    cognitive_style: CognitiveStyle = Field(..., description="认知风格")
    emotional_profile: Dict[str, Any] = Field(..., description="情感档案")
    communication_style: Dict[str, Any] = Field(..., description="沟通风格")

    # Deep insights
    personality_insights: List[PersonalityInsight] = Field(..., description="人格洞察")
    behavior_patterns: List[BehaviorPattern] = Field(..., description="行为模式")
    value_system: ValueSystem = Field(..., description="价值体系")

    # Predictive elements
    decision_patterns: List[DecisionPattern] = Field(..., description="决策模式")
    stress_responses: List[str] = Field(..., description="压力反应")
    growth_areas: List[str] = Field(..., description="成长领域")

# === Analysis Phase Models ===

class DeepAnalysisResult(BaseModel):
    """深度分析结果"""
    psychological_summary: str = Field(..., description="心理学总结")
    personality_updates: PersonalitySnapshot = Field(..., description="人格更新")
    new_insights: List[PersonalityInsight] = Field(..., description="新发现的洞察")
    confidence_score: float = Field(..., ge=0, le=1, description="整体分析置信度")
    
    # Database operations
    postgres_operations: List[Dict[str, Any]] = Field(..., description="PostgreSQL操作")
    neo4j_operations: List[Dict[str, Any]] = Field(..., description="Neo4j操作")
    chromadb_operations: List[Dict[str, Any]] = Field(..., description="ChromaDB操作")

# === Question Generation Models ===

class StrategicQuestion(BaseModel):
    """战略性问题"""
    question_text: str = Field(..., description="问题文本")
    question_type: str = Field(..., description="问题类型")
    target_insight: str = Field(..., description="目标洞察")
    expected_depth: str = Field(..., description="期望深度")
    follow_up_potential: bool = Field(..., description="是否有后续问题潜力")

class QuestioningStrategy(BaseModel):
    """提问策略"""
    overall_goal: str = Field(..., description="总体目标")
    current_focus: str = Field(..., description="当前焦点")
    priority_areas: List[str] = Field(..., description="优先领域")
    strategic_questions: List[StrategicQuestion] = Field(..., description="战略问题")
    session_plan: str = Field(..., description="会话计划")

# === Similarity Assessment Models ===

class SimilarityMetric(BaseModel):
    """相似度指标"""
    metric_name: str = Field(..., description="指标名称")
    score: float = Field(..., ge=0, le=1, description="相似度得分")
    weight: float = Field(..., ge=0, le=1, description="指标权重")
    details: str = Field(..., description="详细说明")

class PersonalitySimilarity(BaseModel):
    """人格相似度评估"""
    overall_similarity: float = Field(..., ge=0, le=1, description="总体相似度")
    dimension_similarities: List[SimilarityMetric] = Field(..., description="维度相似度")
    behavioral_similarity: float = Field(..., ge=0, le=1, description="行为相似度")
    communication_similarity: float = Field(..., ge=0, le=1, description="沟通相似度")
    value_alignment: float = Field(..., ge=0, le=1, description="价值观一致性")
    
    strengths: List[str] = Field(..., description="相似性优势")
    gaps: List[str] = Field(..., description="差距领域")
    improvement_suggestions: List[str] = Field(..., description="改进建议")

# === Legacy Models (for compatibility) ===

class InterrogationPlan(BaseModel):
    overall_strategy: str = Field(..., description="总体策略")
    questioning_strategy: QuestioningStrategy = Field(..., description="提问策略")

class EmpatheticQuestion(BaseModel):
    question_text: str = Field(..., description="共情问题文本")
    question_context: str = Field(..., description="问题背景")
    expected_response_type: str = Field(..., description="期望回应类型")
</file>

<file path="backend/app/services/__init__.py">
# 服务模块
</file>

<file path="backend/app/services/enhanced_socratic_cycle.py">
"""
Enhanced Socratic cycle for 100% personality cloning
Implements THINK -> ASK -> ANALYZE -> PREDICT -> VALIDATE cycle
"""

import json
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from app.services.personality_analyzer import PersonalityAnalyzer
from app.database.models import (
    PersonalityProfile, Conversation, Message, User,
    Entity, Belief, Event, CognitivePattern, LanguagePattern, EmotionalResponse
)
from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    PersonalitySnapshot, SituationalResponse, PersonalitySimilarity
)
import structlog

logger = structlog.get_logger()

class EnhancedSocraticCycle:
    def __init__(self):
        self.analyzer = PersonalityAnalyzer()
    
    async def think_phase(
        self, 
        personality_id: str, 
        db: AsyncSession
    ) -> QuestioningStrategy:
        """
        Enhanced THINK phase: Analyze current personality state and plan next questions
        """
        try:
            # Get current personality profile
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality = result.scalar_one_or_none()
            
            if not personality:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get completion status
            completion_status = await self.analyzer.get_personality_completion_status(
                personality_id, db
            )
            
            # Collect current insights
            collected_insights = await self._collect_current_insights(personality_id, db)
            
            # Identify missing information areas
            missing_areas = self._identify_missing_areas(completion_status, collected_insights)
            
            # Get recent conversation history
            conversation_history = await self._get_recent_conversation_history(
                personality_id, db, limit=10
            )
            
            # Generate strategic questioning plan
            strategy = await self.analyzer.generate_strategic_questions(
                target_name=personality.target_name,
                completion_percentage=completion_status["completion_percentage"],
                collected_insights=collected_insights,
                current_focus_areas=missing_areas,
                missing_information=missing_areas,
                conversation_history=conversation_history
            )
            
            logger.info(
                "THINK phase completed",
                personality_id=personality_id,
                completion_percentage=completion_status["completion_percentage"],
                strategy_goal=strategy.overall_goal
            )
            
            return strategy
            
        except Exception as e:
            logger.error("THINK phase failed", error=str(e), personality_id=personality_id)
            raise

    async def ask_phase(
        self, 
        strategy: QuestioningStrategy, 
        conversation_history: List[str]
    ) -> EmpatheticQuestion:
        """
        Enhanced ASK phase: Generate empathetic question based on strategy
        """
        try:
            # Select the highest priority question from strategy
            if not strategy.questioning_strategy.strategic_questions:
                return EmpatheticQuestion(
                    question_text="请告诉我更多关于这个人的信息。",
                    question_context="通用询问",
                    expected_response_type="描述性"
                )
            
            priority_question = strategy.questioning_strategy.strategic_questions[0]
            
            # Generate empathetic version of the strategic question
            empathetic_question = await self.analyzer.generate_empathetic_question(
                strategic_goal=strategy.overall_goal,
                target_insight=priority_question.target_insight,
                conversation_history=conversation_history
            )
            
            logger.info(
                "ASK phase completed",
                question_type=priority_question.question_type,
                target_insight=priority_question.target_insight
            )
            
            return empathetic_question
            
        except Exception as e:
            logger.error("ASK phase failed", error=str(e))
            raise

    async def analyze_phase(
        self, 
        user_input: str, 
        personality_id: str,
        conversation_history: List[str],
        db: AsyncSession
    ) -> DeepAnalysisResult:
        """
        Enhanced ANALYZE phase: Deep personality analysis and database updates
        """
        try:
            # Get current personality state
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get current personality state as dict
            current_state = await self._get_personality_state_dict(personality_data, db)
            
            # Perform deep analysis
            analysis_result = await self.analyzer.analyze_personality_deep(
                user_narrative=user_input,
                conversation_history=conversation_history,
                target_name=personality_data.target_name,
                current_personality_state=current_state,
                db=db
            )
            
            # Execute database operations
            await self._execute_database_operations(
                analysis_result, personality_id, db
            )
            
            # Update personality completion percentage
            completion_status = await self.analyzer.get_personality_completion_status(
                personality_id, db
            )
            personality_data.completion_percentage = completion_status["completion_percentage"]
            
            logger.info(
                "ANALYZE phase completed",
                personality_id=personality_id,
                confidence_score=analysis_result.confidence_score,
                new_insights_count=len(analysis_result.new_insights)
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error("ANALYZE phase failed", error=str(e), personality_id=personality_id)
            raise

    async def predict_phase(
        self,
        personality_id: str,
        situation: str,
        db: AsyncSession
    ) -> SituationalResponse:
        """
        PREDICT phase: Predict personality response to given situation
        """
        try:
            # Get personality profile
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            # Get complete personality state
            personality_state = await self._get_personality_state_dict(personality_data, db)
            
            # Generate prediction
            prediction = await self.analyzer.predict_personality_response(
                target_name=personality_data.target_name,
                personality_profile=personality_state,
                situation_description=situation
            )
            
            logger.info(
                "PREDICT phase completed",
                personality_id=personality_id,
                confidence=prediction.confidence
            )
            
            return prediction
            
        except Exception as e:
            logger.error("PREDICT phase failed", error=str(e), personality_id=personality_id)
            raise

    async def validate_phase(
        self,
        personality_id: str,
        reference_data: Optional[Dict[str, Any]],
        db: AsyncSession
    ) -> PersonalitySimilarity:
        """
        VALIDATE phase: Assess personality cloning accuracy
        """
        try:
            # Get constructed personality
            personality = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            personality_data = personality.scalar_one_or_none()
            
            if not personality_data:
                raise ValueError(f"Personality profile {personality_id} not found")
            
            constructed_personality = await self._get_personality_state_dict(personality_data, db)
            
            # Assess similarity
            similarity = await self.analyzer.assess_personality_similarity(
                target_name=personality_data.target_name,
                constructed_personality=constructed_personality,
                reference_personality=reference_data
            )
            
            logger.info(
                "VALIDATE phase completed",
                personality_id=personality_id,
                overall_similarity=similarity.overall_similarity
            )
            
            return similarity
            
        except Exception as e:
            logger.error("VALIDATE phase failed", error=str(e), personality_id=personality_id)
            raise

    # Helper methods
    
    async def _collect_current_insights(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """Collect current personality insights from database"""
        insights = {
            "entities": [],
            "beliefs": [],
            "events": [],
            "cognitive_patterns": [],
            "emotional_responses": []
        }
        
        # Get entities
        entities_result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        insights["entities"] = [
            {
                "name": entity.name,
                "type": entity.entity_type,
                "relationship": entity.relationship_type,
                "importance": entity.importance_score
            }
            for entity in entities_result.scalars().all()
        ]
        
        # Get beliefs
        beliefs_result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        insights["beliefs"] = [
            {
                "statement": belief.statement,
                "category": belief.belief_category,
                "conviction": belief.conviction_strength
            }
            for belief in beliefs_result.scalars().all()
        ]
        
        # Get events
        events_result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        insights["events"] = [
            {
                "title": event.title,
                "age": event.age_at_event,
                "type": event.event_type,
                "impact": event.emotional_impact
            }
            for event in events_result.scalars().all()
        ]
        
        return insights

    def _identify_missing_areas(
        self, 
        completion_status: Dict[str, Any], 
        insights: Dict[str, Any]
    ) -> List[str]:
        """Identify areas that need more exploration"""
        missing_areas = []
        
        # Check completion factors
        for area, score in completion_status.get("completion_factors", {}).items():
            if score < 0.7:
                missing_areas.append(area)
        
        # Check insight depth
        if len(insights.get("entities", [])) < 5:
            missing_areas.append("relationship_network")
        
        if len(insights.get("beliefs", [])) < 3:
            missing_areas.append("value_system")
        
        if len(insights.get("events", [])) < 5:
            missing_areas.append("life_experiences")
        
        return missing_areas

    async def _get_recent_conversation_history(
        self, 
        personality_id: str, 
        db: AsyncSession, 
        limit: int = 10
    ) -> List[str]:
        """Get recent conversation history"""
        messages_result = await db.execute(
            select(Message)
            .join(Conversation)
            .where(Conversation.personality_id == personality_id)
            .order_by(Message.timestamp.desc())
            .limit(limit)
        )
        
        messages = messages_result.scalars().all()
        return [f"{msg.sender}: {msg.content}" for msg in reversed(messages)]

    async def _get_personality_state_dict(
        self, 
        personality: PersonalityProfile, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Convert personality profile to dictionary"""
        return {
            "target_name": personality.target_name,
            "completion_percentage": personality.completion_percentage,
            "big_five": {
                "openness": personality.openness_score,
                "conscientiousness": personality.conscientiousness_score,
                "extraversion": personality.extraversion_score,
                "agreeableness": personality.agreeableness_score,
                "neuroticism": personality.neuroticism_score
            },
            "cognitive_style": personality.dominant_cognitive_style.value if personality.dominant_cognitive_style else None,
            "decision_speed": personality.decision_making_speed,
            "risk_tolerance": personality.risk_tolerance,
            "communication": {
                "response_length": personality.average_response_length,
                "vocabulary_complexity": personality.vocabulary_complexity,
                "emotional_expressiveness": personality.emotional_expressiveness
            }
        }

    async def _execute_database_operations(
        self, 
        analysis_result: DeepAnalysisResult, 
        personality_id: str, 
        db: AsyncSession
    ):
        """Execute database operations from analysis result"""
        try:
            # Execute PostgreSQL operations
            for op in analysis_result.postgres_operations:
                await self._execute_postgres_operation(op, personality_id, db)
            
            # Note: Neo4j and ChromaDB operations would be implemented here
            # For now, we focus on PostgreSQL operations
            
            await db.commit()
            
        except Exception as e:
            await db.rollback()
            logger.error("Database operations failed", error=str(e))
            raise

    async def _execute_postgres_operation(
        self,
        operation: Dict[str, Any],
        personality_id: str,
        db: AsyncSession
    ):
        """Execute a single PostgreSQL operation based on LLM analysis"""
        op_type = operation.get("type")
        op_data = operation.get("data")

        if not op_type or not op_data:
            logger.warning("Invalid database operation format", operation=operation)
            return

        try:
            if op_type == "add_belief":
                new_belief = Belief(
                    personality_id=personality_id,
                    statement=op_data.get("statement"),
                    belief_category=op_data.get("category", "personal"),
                    conviction_strength=op_data.get("conviction", 0.7),
                    flexibility_score=op_data.get("flexibility", 0.5),
                    origin_context=op_data.get("origin_context"),
                    full_explanation=op_data.get("explanation")
                )
                db.add(new_belief)
                logger.info("Added new belief", belief=op_data.get("statement"))

            elif op_type == "add_event":
                new_event = Event(
                    personality_id=personality_id,
                    title=op_data.get("title"),
                    age_at_event=op_data.get("age"),
                    life_stage=op_data.get("life_stage"),
                    event_type=op_data.get("event_type", "general"),
                    emotional_impact=op_data.get("emotional_impact", 0.0),
                    centrality_score=op_data.get("centrality", 0.5),
                    memory_vividness=op_data.get("vividness", 0.5),
                    lessons_learned=op_data.get("lessons", []),
                    full_narrative=op_data.get("narrative")
                )
                db.add(new_event)
                logger.info("Added new event", event=op_data.get("title"))

            elif op_type == "add_entity":
                new_entity = Entity(
                    personality_id=personality_id,
                    name=op_data.get("name"),
                    entity_type=op_data.get("entity_type", "person"),
                    relationship_type=op_data.get("relationship_type"),
                    emotional_valence=op_data.get("emotional_valence", 0.0),
                    importance_score=op_data.get("importance", 0.5),
                    profile=op_data.get("profile", {})
                )
                db.add(new_entity)
                logger.info("Added new entity", entity=op_data.get("name"))

            elif op_type == "update_big_five":
                result = await db.execute(
                    select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
                )
                profile = result.scalar_one_or_none()
                if profile:
                    profile.openness_score = op_data.get("openness", profile.openness_score)
                    profile.conscientiousness_score = op_data.get("conscientiousness", profile.conscientiousness_score)
                    profile.extraversion_score = op_data.get("extraversion", profile.extraversion_score)
                    profile.agreeableness_score = op_data.get("agreeableness", profile.agreeableness_score)
                    profile.neuroticism_score = op_data.get("neuroticism", profile.neuroticism_score)
                    logger.info("Updated Big Five scores", profile_id=personality_id)

            elif op_type == "add_family_member":
                # 导入FamilyMember模型
                from app.database.models import FamilyMember
                new_family_member = FamilyMember(
                    personality_id=personality_id,
                    relationship_type=op_data.get("relationship"),
                    name=op_data.get("name"),
                    personality_summary=op_data.get("personality_summary", {}),
                    parenting_style=op_data.get("parenting_style")
                )
                db.add(new_family_member)
                logger.info("Added family member", relationship=op_data.get("relationship"))

            elif op_type == "update_attachment_style":
                result = await db.execute(
                    select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
                )
                profile = result.scalar_one_or_none()
                if profile:
                    profile.attachment_style = op_data.get("attachment_style", profile.attachment_style)
                    logger.info("Updated attachment style", style=op_data.get("attachment_style"))

            else:
                logger.warning("Unknown operation type", op_type=op_type)

        except Exception as e:
            logger.error("Failed to execute postgres operation", error=str(e), operation=operation)
            raise
</file>

<file path="backend/app/services/personality_analyzer.py">
"""
Advanced personality analysis service for 100% personality cloning
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from google import genai
from google.genai import types
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from dotenv import load_dotenv

from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    PersonalitySnapshot, SituationalResponse, PersonalitySimilarity,
    BehaviorPattern, ValueSystem, CognitiveStyle
)
from app.llm.prompts import (
    PERSONALITY_ANALYSIS_PROMPT, STRATEGIC_QUESTIONING_PROMPT,
    EMPATHETIC_QUESTION_PROMPT, PERSONALITY_PREDICTION_PROMPT,
    SIMILARITY_ASSESSMENT_PROMPT, BEHAVIOR_PATTERN_ANALYSIS_PROMPT,
    VALUE_SYSTEM_ANALYSIS_PROMPT, COGNITIVE_STYLE_ANALYSIS_PROMPT
)
from app.database.models import PersonalityProfile, Message, Conversation

load_dotenv()

class PersonalityAnalyzer:
    def __init__(self):
        # Initialize Google GenAI client (new SDK)
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            self.client = genai.Client(api_key=api_key)
            self.available = True
        else:
            self.client = None
            self.available = False

        # Fallback to OpenAI if available
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "YOUR_OPENAI_API_KEY_HERE":
            try:
                import instructor
                import openai
                self.openai_client = instructor.from_openai(openai.OpenAI(api_key=openai_key))
            except ImportError:
                self.openai_client = None
        else:
            self.openai_client = None
        
    async def safe_llm_call(self, prompt: str, response_model, max_retries: int = 3):
        """Safe LLM call with retry mechanism and fallback"""
        if not self.available:
            return self._get_fallback_response(response_model)

        for attempt in range(max_retries):
            try:
                # Use new Google GenAI SDK with structured output
                response = self.client.models.generate_content(
                    model='gemini-2.0-flash-001',
                    contents=prompt,
                    config=types.GenerateContentConfig(
                        response_mime_type='application/json',
                        response_schema=response_model,
                        max_output_tokens=2000,
                        temperature=0.7
                    )
                )

                # Parse the JSON response
                import json
                result_data = json.loads(response.text)
                return response_model(**result_data)

            except Exception as e:
                print(f"LLM call attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    return self._get_fallback_response(response_model)
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
    
    def _get_fallback_response(self, response_model):
        """Generate fallback response when LLM fails"""
        if response_model == DeepAnalysisResult:
            return DeepAnalysisResult(
                psychological_summary="分析暂时不可用，请稍后重试。",
                personality_updates=PersonalitySnapshot(
                    target_name="Unknown",
                    analysis_timestamp="",
                    completion_percentage=0.0,
                    big_five=[],
                    cognitive_style=CognitiveStyle(
                        primary_style="unknown",
                        decision_speed=0.5,
                        risk_tolerance=0.5,
                        analytical_depth=0.5
                    ),
                    emotional_profile={},
                    communication_style={},
                    personality_insights=[],
                    behavior_patterns=[],
                    value_system=ValueSystem(
                        core_values=[],
                        value_hierarchy=[],
                        moral_framework="",
                        ethical_boundaries=[],
                        value_conflicts=[]
                    ),
                    decision_patterns=[],
                    stress_responses=[],
                    growth_areas=[]
                ),
                new_insights=[],
                confidence_score=0.0,
                postgres_operations=[],
                neo4j_operations=[],
                chromadb_operations=[]
            )
        elif response_model == EmpatheticQuestion:
            return EmpatheticQuestion(
                question_text="请告诉我更多关于这个人的信息。",
                question_context="通用询问",
                expected_response_type="描述性"
            )
        # Add more fallback responses as needed
        return None

    async def analyze_personality_deep(
        self, 
        user_narrative: str, 
        conversation_history: List[str],
        target_name: str,
        current_personality_state: Dict[str, Any],
        db: AsyncSession
    ) -> DeepAnalysisResult:
        """Perform deep personality analysis"""
        
        prompt = PERSONALITY_ANALYSIS_PROMPT.format(
            target_name=target_name,
            user_narrative=user_narrative,
            conversation_history="\n".join(conversation_history),
            current_personality_state=json.dumps(current_personality_state, ensure_ascii=False, indent=2)
        )
        
        result = await self.safe_llm_call(prompt, DeepAnalysisResult)
        return result

    async def generate_strategic_questions(
        self,
        target_name: str,
        completion_percentage: float,
        collected_insights: Dict[str, Any],
        current_focus_areas: List[str],
        missing_information: List[str],
        conversation_history: List[str]
    ) -> QuestioningStrategy:
        """Generate strategic questioning plan"""
        
        prompt = STRATEGIC_QUESTIONING_PROMPT.format(
            target_name=target_name,
            completion_percentage=completion_percentage,
            collected_insights=json.dumps(collected_insights, ensure_ascii=False, indent=2),
            current_focus_areas=", ".join(current_focus_areas),
            missing_information=", ".join(missing_information),
            conversation_history="\n".join(conversation_history)
        )
        
        result = await self.safe_llm_call(prompt, QuestioningStrategy)
        return result

    async def generate_empathetic_question(
        self,
        strategic_goal: str,
        target_insight: str,
        conversation_history: List[str]
    ) -> EmpatheticQuestion:
        """Generate empathetic question based on strategy"""
        
        prompt = EMPATHETIC_QUESTION_PROMPT.format(
            strategic_goal=strategic_goal,
            target_insight=target_insight,
            conversation_history="\n".join(conversation_history)
        )
        
        result = await self.safe_llm_call(prompt, EmpatheticQuestion)
        return result

    async def predict_personality_response(
        self,
        target_name: str,
        personality_profile: Dict[str, Any],
        situation_description: str
    ) -> SituationalResponse:
        """Predict how the target person would respond in a given situation"""
        
        prompt = PERSONALITY_PREDICTION_PROMPT.format(
            target_name=target_name,
            personality_profile=json.dumps(personality_profile, ensure_ascii=False, indent=2),
            situation_description=situation_description
        )
        
        result = await self.safe_llm_call(prompt, SituationalResponse)
        return result

    async def assess_personality_similarity(
        self,
        target_name: str,
        constructed_personality: Dict[str, Any],
        reference_personality: Optional[Dict[str, Any]] = None
    ) -> PersonalitySimilarity:
        """Assess similarity between constructed and reference personality"""
        
        prompt = SIMILARITY_ASSESSMENT_PROMPT.format(
            target_name=target_name,
            constructed_personality=json.dumps(constructed_personality, ensure_ascii=False, indent=2),
            reference_personality=json.dumps(reference_personality or {}, ensure_ascii=False, indent=2)
        )
        
        result = await self.safe_llm_call(prompt, PersonalitySimilarity)
        return result

    async def analyze_behavior_patterns(
        self,
        target_name: str,
        user_narrative: str
    ) -> List[BehaviorPattern]:
        """Analyze behavior patterns from narrative"""
        
        prompt = BEHAVIOR_PATTERN_ANALYSIS_PROMPT.format(
            target_name=target_name,
            user_narrative=user_narrative
        )
        
        result = await self.safe_llm_call(prompt, BehaviorPattern)
        return [result] if result else []

    async def analyze_value_system(
        self,
        target_name: str,
        relevant_narratives: List[str]
    ) -> ValueSystem:
        """Analyze value system and moral framework"""
        
        prompt = VALUE_SYSTEM_ANALYSIS_PROMPT.format(
            target_name=target_name,
            relevant_narratives="\n".join(relevant_narratives)
        )
        
        result = await self.safe_llm_call(prompt, ValueSystem)
        return result

    async def analyze_cognitive_style(
        self,
        target_name: str,
        cognitive_information: str
    ) -> CognitiveStyle:
        """Analyze cognitive style and thinking patterns"""
        
        prompt = COGNITIVE_STYLE_ANALYSIS_PROMPT.format(
            target_name=target_name,
            cognitive_information=cognitive_information
        )
        
        result = await self.safe_llm_call(prompt, CognitiveStyle)
        return result

    async def get_personality_completion_status(
        self,
        personality_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Calculate personality analysis completion status"""
        
        # Query database for current personality data
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            return {"completion_percentage": 0.0, "missing_areas": []}
        
        # Calculate completion based on available data
        completion_factors = {
            "basic_info": 1.0 if personality.target_name else 0.0,
            "big_five": sum([
                1.0 if personality.openness_score != 0.5 else 0.0,
                1.0 if personality.conscientiousness_score != 0.5 else 0.0,
                1.0 if personality.extraversion_score != 0.5 else 0.0,
                1.0 if personality.agreeableness_score != 0.5 else 0.0,
                1.0 if personality.neuroticism_score != 0.5 else 0.0,
            ]) / 5.0,
            "cognitive_style": 1.0 if personality.dominant_cognitive_style else 0.0,
            "communication": 1.0 if personality.average_response_length else 0.0,
        }
        
        overall_completion = sum(completion_factors.values()) / len(completion_factors) * 100
        
        missing_areas = [
            area for area, score in completion_factors.items() if score < 0.8
        ]
        
        return {
            "completion_percentage": overall_completion,
            "missing_areas": missing_areas,
            "completion_factors": completion_factors
        }
</file>

<file path="backend/app/services/personality_simulator.py">
"""
PersonalitySimulator - 西牟拉胡协议的核心实现
负责AI角色扮演和人格模拟
"""

import json
import os
import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

import google.generativeai as genai
from dotenv import load_dotenv

from app.database.models import (
    PersonalityProfile, Entity, Belief, Event,
    FamilyMember, CognitivePattern, EmotionalResponse, Conversation
)
from app.llm.prompts import SIMULATION_GENESIS_PROMPT
import structlog

# 加载环境变量
load_dotenv()

logger = structlog.get_logger()

class PersonalitySimulator:
    """AI人格模拟器 - 让AI完全代入目标人物身份"""

    def __init__(self):
        # 初始化 Google GenAI client
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "YOUR_GEMINI_API_KEY_HERE":
            # 配置 Gemini API
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.available = True
        else:
            self.model = None
            self.available = False
        logger.info(f"PersonalitySimulator initialized. LLM available: {self.available}")
    
    async def generate_response(
        self,
        personality_id: str,
        user_input: str,
        db: AsyncSession,
        conversation_id: str, # 新增 conversation_id
        conversation_history: List[str] = None
    ) -> str:
        """
        生成目标人物的回复
        这是西牟拉胡协议的核心方法
        """
        try:
            # 1. 从数据库加载完整的人格档案
            profile_data = await self._load_full_profile(personality_id, db)
            
            if not profile_data:
                return "抱歉，无法找到对应的人格档案。"
            
            # 新增: 加载 conversation 对象
            conv_result = await db.execute(select(Conversation).where(Conversation.conversation_id == conversation_id))
            conversation = conv_result.scalar_one_or_none()
            if not conversation:
                raise ValueError("Conversation not found for dynamic state update.")

            # 2. 构建动态状态
            dynamic_state = await self._calculate_dynamic_state(profile_data, user_input, conversation, db) # 传递 conversation
            
            # 3. 检索相关记忆（暂时使用简化版本）
            retrieved_memories = await self._retrieve_relevant_memories(
                profile_data, user_input, db
            )
            
            # 4. 组装Genesis Prompt
            prompt = await self._build_genesis_prompt(
                profile_data, dynamic_state, retrieved_memories, 
                user_input, conversation_history or []
            )
            
            # 5. 调用LLM生成回复
            response = await self._call_llm(prompt)
            
            logger.info(
                "Generated personality response",
                personality_id=personality_id,
                target_name=profile_data['target_name']
            )
            
            return response
            
        except Exception as e:
            logger.error("Failed to generate personality response", error=str(e))
            return f"作为{profile_data.get('target_name', '未知')}，我现在有些困惑，请稍后再试。"
    
    async def _load_full_profile(self, personality_id: str, db: AsyncSession) -> Dict[str, Any]:
        """从数据库加载完整的人格档案数据"""
        try:
            # 加载基础人格档案
            result = await db.execute(
                select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
            )
            profile = result.scalar_one_or_none()
            
            if not profile:
                return None
            
            # 加载相关数据
            beliefs = await self._load_beliefs(personality_id, db)
            entities = await self._load_entities(personality_id, db)
            events = await self._load_events(personality_id, db)
            family_members = await self._load_family_members(personality_id, db)
            
            return {
                'target_name': profile.target_name,
                'description': profile.description,
                'big_five': {
                    'openness': profile.openness_score or 0.5,
                    'conscientiousness': profile.conscientiousness_score or 0.5,
                    'extraversion': profile.extraversion_score or 0.5,
                    'agreeableness': profile.agreeableness_score or 0.5,
                    'neuroticism': profile.neuroticism_score or 0.5
                },
                'attachment_style': profile.attachment_style or 'secure',
                'cultural_background': profile.cultural_background or {},
                'beliefs': beliefs,
                'entities': entities,
                'events': events,
                'family_members': family_members,
                'communication_style': {
                    'response_length': profile.average_response_length or 0.5,
                    'vocabulary_complexity': profile.vocabulary_complexity or 0.5,
                    'emotional_expressiveness': profile.emotional_expressiveness or 0.5
                }
            }
            
        except Exception as e:
            logger.error("Failed to load full profile", error=str(e))
            return None
    
    async def _load_beliefs(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载信念数据"""
        result = await db.execute(
            select(Belief).where(Belief.personality_id == personality_id)
        )
        beliefs = result.scalars().all()
        return [
            {
                'statement': belief.statement,
                'category': belief.belief_category,
                'conviction': belief.conviction_strength,
                'explanation': belief.full_explanation
            }
            for belief in beliefs
        ]
    
    async def _load_entities(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载重要人物/实体数据"""
        result = await db.execute(
            select(Entity).where(Entity.personality_id == personality_id)
        )
        entities = result.scalars().all()
        return [
            {
                'name': entity.name,
                'type': entity.entity_type,
                'relationship': entity.relationship_type,
                'emotional_valence': entity.emotional_valence,
                'importance': entity.importance_score
            }
            for entity in entities
        ]
    
    async def _load_events(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载关键人生事件"""
        result = await db.execute(
            select(Event).where(Event.personality_id == personality_id)
        )
        events = result.scalars().all()
        return [
            {
                'title': event.title,
                'age': event.age_at_event,
                'type': event.event_type,
                'emotional_impact': event.emotional_impact,
                'narrative': event.full_narrative
            }
            for event in events
        ]
    
    async def _load_family_members(self, personality_id: str, db: AsyncSession) -> List[Dict]:
        """加载家庭成员信息"""
        result = await db.execute(
            select(FamilyMember).where(FamilyMember.personality_id == personality_id)
        )
        family_members = result.scalars().all()
        return [
            {
                'relationship': member.relationship_type,
                'name': member.name,
                'personality_summary': member.personality_summary,
                'parenting_style': member.parenting_style
            }
            for member in family_members
        ]
    
    async def _calculate_dynamic_state(
        self,
        profile_data: Dict,
        user_input: str,
        conversation: Conversation,
        db: AsyncSession
    ) -> Dict:
        """计算并更新当前的动态状态（情绪、关系等）"""

        # 1. 从数据库加载当前会话状态，如果为空则初始化
        session_data = conversation.session_data or {}
        dynamic_state = {
            'current_mood': session_data.get('mood', '平静'),
            'energy': session_data.get('energy', 80),
            'intimacy': session_data.get('intimacy', 50),
            'trust': session_data.get('trust', 60)
        }

        # 2. 基于用户输入进行简单的情绪和关系调整 (这是一个可以无限深化的点)
        # 例如:
        positive_words = ["喜欢", "开心", "太棒了", "感谢", "爱", "好", "棒", "赞", "谢谢"]
        negative_words = ["讨厌", "失望", "糟糕", "恨", "坏", "烦", "差", "不好", "生气"]

        if any(word in user_input for word in positive_words):
            dynamic_state['intimacy'] = min(100, dynamic_state['intimacy'] + 2)
            dynamic_state['trust'] = min(100, dynamic_state['trust'] + 1)
            dynamic_state['current_mood'] = '开心'
            dynamic_state['energy'] = min(100, dynamic_state['energy'] + 5)
        elif any(word in user_input for word in negative_words):
            dynamic_state['intimacy'] = max(0, dynamic_state['intimacy'] - 3)
            dynamic_state['trust'] = max(0, dynamic_state['trust'] - 2)
            dynamic_state['current_mood'] = '低落'
            dynamic_state['energy'] = max(20, dynamic_state['energy'] - 10)

        # 3. 将更新后的状态写回数据库
        conversation.session_data = dynamic_state
        db.add(conversation)
        await db.flush() # 确保在提交前更新

        return dynamic_state
    
    async def _retrieve_relevant_memories(
        self, 
        profile_data: Dict, 
        user_input: str, 
        db: AsyncSession
    ) -> str:
        """检索与当前对话相关的记忆（简化版本）"""
        # 这里应该实现RAG检索，暂时返回最重要的几个记忆
        memories = []
        
        # 添加重要事件
        for event in profile_data.get('events', [])[:3]:
            if event.get('narrative'):
                memories.append(f"重要经历：{event['narrative']}")
        
        # 添加核心信念
        for belief in profile_data.get('beliefs', [])[:2]:
            if belief.get('statement'):
                memories.append(f"核心信念：{belief['statement']}")
        
        return '\n'.join(memories) if memories else "暂无相关记忆。"
    
    async def _build_genesis_prompt(
        self,
        profile_data: Dict,
        dynamic_state: Dict,
        retrieved_memories: str,
        user_input: str,
        conversation_history: List[str]
    ) -> str:
        """构建Genesis Prompt"""
        
        # 格式化核心信念
        core_beliefs = '\n'.join([
            f"- {belief['statement']}" 
            for belief in profile_data.get('beliefs', [])
        ]) or "暂无明确的核心信念记录。"
        
        # 格式化重要关系
        important_relationships = '\n'.join([
            f"- {entity['name']} ({entity['relationship']}): {entity.get('type', '重要人物')}"
            for entity in profile_data.get('entities', [])
        ]) or "暂无重要人际关系记录。"
        
        # 格式化关键事件
        key_life_events = '\n'.join([
            f"- {event['title']} (年龄{event['age']}): {event.get('narrative', '重要经历')}"
            for event in profile_data.get('events', [])
        ]) or "暂无关键人生事件记录。"
        
        # 格式化对话历史
        conversation_context = '\n'.join(conversation_history[-5:]) if conversation_history else "这是我们的第一次对话。"
        
        return SIMULATION_GENESIS_PROMPT.format(
            target_name=profile_data['target_name'],
            openness=profile_data['big_five']['openness'],
            conscientiousness=profile_data['big_five']['conscientiousness'],
            extraversion=profile_data['big_five']['extraversion'],
            agreeableness=profile_data['big_five']['agreeableness'],
            neuroticism=profile_data['big_five']['neuroticism'],
            attachment_style=profile_data['attachment_style'],
            cultural_background=json.dumps(profile_data['cultural_background'], ensure_ascii=False),
            core_beliefs=core_beliefs,
            important_relationships=important_relationships,
            key_life_events=key_life_events,
            current_mood=dynamic_state['current_mood'],
            energy=dynamic_state['energy'],
            intimacy=dynamic_state['intimacy'],
            trust=dynamic_state['trust'],
            retrieved_memories=retrieved_memories,
            conversation_history=conversation_context,
            user_input=user_input
        )
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM生成回复 (使用原生异步方法)"""
        if not self.available:
            logger.warning("LLM client not available, returning fallback response.")
            return "我理解你的话，但我的思考核心暂时无法连接。请检查API Key配置。（测试模式）"

        try:
            # 使用原生异步方法 generate_content_async
            response = await self.model.generate_content_async(
                contents=prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.75,
                    max_output_tokens=500,
                )
            )
            return response.text

        except Exception as e:
            logger.error("LLM call failed in simulator", error=str(e))
            return "我脑子里现在有点乱，好像想说些什么，但又说不出来...我们能等会儿再说这个吗？"
</file>

<file path="backend/create_demo_user.py">
#!/usr/bin/env python3
"""
创建演示用户和数据的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.database.db_session import get_db_session, init_database
from app.database.models import User, PersonalityProfile, Entity, Belief, Event
import uuid

async def create_demo_user():
    """创建演示用户和数据"""
    print("🔄 正在创建演示用户...")
    
    try:
        # 初始化数据库
        await init_database()
        
        # 获取数据库会话
        async for db in get_db_session():
            # 检查演示用户是否已存在
            result = await db.execute(select(User).where(User.username == "demo"))
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print("✅ 演示用户已存在")
                print(f"   用户ID: {existing_user.user_id}")
                print(f"   用户名: {existing_user.username}")
                print(f"   邮箱: {existing_user.email}")
                return existing_user
            
            # 创建演示用户
            demo_user = User(
                username="demo",
                email="<EMAIL>",
                hashed_password="demo123",  # 简化版，实际应该加密
                is_active=True
            )
            
            db.add(demo_user)
            await db.commit()
            await db.refresh(demo_user)
            
            print("✅ 演示用户创建成功")
            print(f"   用户ID: {demo_user.user_id}")
            print(f"   用户名: {demo_user.username}")
            print(f"   邮箱: {demo_user.email}")
            print(f"   密码: demo123")
            
            # 创建演示人格档案
            demo_personality = PersonalityProfile(
                user_id=demo_user.user_id,
                target_name="演示人格",
                description="这是一个演示用的人格档案，展示系统的各项功能",
                completion_percentage=75.0,
                openness_score=0.7,
                conscientiousness_score=0.6,
                extraversion_score=0.8,
                agreeableness_score=0.9,
                neuroticism_score=0.3,
                decision_making_speed=0.6,
                risk_tolerance=0.4,
                average_response_length=150.0,
                vocabulary_complexity=0.7,
                emotional_expressiveness=0.8
            )
            
            db.add(demo_personality)
            await db.commit()
            await db.refresh(demo_personality)
            
            print("✅ 演示人格档案创建成功")
            print(f"   档案ID: {demo_personality.profile_id}")
            print(f"   目标人物: {demo_personality.target_name}")
            print(f"   完成度: {demo_personality.completion_percentage}%")
            
            # 创建一些演示实体
            demo_entities = [
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="家人",
                    entity_type="person",
                    relationship_type="family",
                    emotional_valence=0.9,
                    importance_score=0.95,
                    profile={"description": "最重要的人际关系"}
                ),
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="朋友",
                    entity_type="person", 
                    relationship_type="friend",
                    emotional_valence=0.8,
                    importance_score=0.8,
                    profile={"description": "社交圈中的重要关系"}
                ),
                Entity(
                    personality_id=demo_personality.profile_id,
                    name="工作",
                    entity_type="concept",
                    relationship_type="professional",
                    emotional_valence=0.6,
                    importance_score=0.7,
                    profile={"description": "职业发展相关"}
                )
            ]
            
            for entity in demo_entities:
                db.add(entity)
            
            # 创建一些演示信念
            demo_beliefs = [
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="诚实是最重要的品质",
                    belief_category="moral",
                    conviction_strength=0.9,
                    flexibility_score=0.2,
                    origin_context="从小的家庭教育",
                    full_explanation="认为诚实是人际关系的基础"
                ),
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="努力工作会有回报",
                    belief_category="personal",
                    conviction_strength=0.8,
                    flexibility_score=0.4,
                    origin_context="个人经历总结",
                    full_explanation="相信通过努力可以实现目标"
                ),
                Belief(
                    personality_id=demo_personality.profile_id,
                    statement="家庭比事业更重要",
                    belief_category="personal",
                    conviction_strength=0.7,
                    flexibility_score=0.6,
                    origin_context="生活价值观",
                    full_explanation="认为家庭幸福是人生的根本"
                )
            ]
            
            for belief in demo_beliefs:
                db.add(belief)
            
            # 创建一些演示事件
            demo_events = [
                Event(
                    personality_id=demo_personality.profile_id,
                    title="大学毕业",
                    age_at_event=22,
                    life_stage="young_adult",
                    event_type="achievement",
                    emotional_impact=0.8,
                    centrality_score=0.9,
                    memory_vividness=0.9,
                    lessons_learned=["坚持的重要性", "知识的价值"],
                    full_narrative="经过四年的努力学习，终于顺利毕业了"
                ),
                Event(
                    personality_id=demo_personality.profile_id,
                    title="第一份工作",
                    age_at_event=23,
                    life_stage="young_adult",
                    event_type="career",
                    emotional_impact=0.7,
                    centrality_score=0.8,
                    memory_vividness=0.8,
                    lessons_learned=["适应能力", "团队合作"],
                    full_narrative="进入职场，开始了人生新的阶段"
                )
            ]
            
            for event in demo_events:
                db.add(event)
            
            await db.commit()
            
            print("✅ 演示数据创建完成")
            print(f"   - 创建了 {len(demo_entities)} 个实体")
            print(f"   - 创建了 {len(demo_beliefs)} 个信念")
            print(f"   - 创建了 {len(demo_events)} 个事件")
            
            return demo_user
            
    except Exception as e:
        print(f"❌ 创建演示用户失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主函数"""
    print("🚀 开始创建演示数据...")
    
    user = await create_demo_user()
    
    if user:
        print("\n🎉 演示数据创建成功！")
        print("\n📝 登录信息:")
        print("   用户名: demo")
        print("   密码: demo123")
        print("\n🌐 现在您可以使用演示账号登录系统了！")
    else:
        print("\n❌ 演示数据创建失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="backend/init_db.py">
"""
数据库初始化脚本
"""

import asyncio
import os
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv

from app.database.models import Base
from app.database.db_session import db_manager

load_dotenv()

async def init_database():
    """初始化数据库表"""
    try:
        print("🔄 正在初始化数据库...")
        
        # 创建所有表
        await db_manager.create_tables()
        
        print("✅ 数据库表创建成功")
        
        # 这里可以添加初始数据
        print("📊 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(init_database())
</file>

<file path="backend/main.py">
"""
Enhanced FastAPI application for 100% personality cloning system
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from pydantic import BaseModel
import structlog

from app.database.db_session import get_db_session, init_database, cleanup_database
from app.database.models import User, PersonalityProfile, Conversation, Message
from app.services.enhanced_socratic_cycle import EnhancedSocraticCycle
from app.llm.schemas import (
    DeepAnalysisResult, QuestioningStrategy, EmpatheticQuestion,
    SituationalResponse, PersonalitySimilarity
)
# 导入模拟API
from app.api.endpoints import simulation

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Application lifecycle management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting personality cloning system")
    await init_database()
    yield
    # Shutdown
    logger.info("Shutting down personality cloning system")
    await cleanup_database()

# Create FastAPI app
app = FastAPI(
    title="100% Personality Cloning System",
    description="Advanced AI system for complete personality replication",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Initialize services
socratic_cycle = EnhancedSocraticCycle()

# Include API routers
app.include_router(simulation.router, prefix="/api/v1", tags=["Simulation"])

# === Pydantic Models ===

class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class PersonalityCreate(BaseModel):
    target_name: str
    description: Optional[str] = None

class ChatRequest(BaseModel):
    user_input: str
    conversation_id: Optional[str] = None

class PredictionRequest(BaseModel):
    situation: str
    personality_id: str

class ValidationRequest(BaseModel):
    personality_id: str
    reference_data: Optional[Dict[str, Any]] = None

# === Authentication ===

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> User:
    """Get current authenticated user"""
    # Simplified authentication - in production, use proper JWT validation
    token = credentials.credentials
    
    # For demo purposes, we'll use a simple token format: "user_id"
    try:
        user_id = token
        result = await db.execute(select(User).where(User.user_id == user_id))
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

# === API Endpoints ===

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "100% Personality Cloning System is running",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.post("/auth/register")
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db_session)
):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_user = await db.execute(
            select(User).where(User.username == user_data.username)
        )
        if existing_user.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        # Create new user (simplified - in production, hash the password)
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=user_data.password  # Should be hashed!
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        logger.info("User registered", user_id=str(new_user.user_id))
        
        return {
            "message": "User registered successfully",
            "user_id": str(new_user.user_id),
            "token": str(new_user.user_id)  # Simplified token
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@app.post("/auth/login")
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db_session)
):
    """Login user"""
    try:
        # Find user
        result = await db.execute(
            select(User).where(User.username == login_data.username)
        )
        user = result.scalar_one_or_none()
        
        if not user or user.hashed_password != login_data.password:  # Simplified check
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        logger.info("User logged in", user_id=str(user.user_id))
        
        return {
            "message": "Login successful",
            "user_id": str(user.user_id),
            "token": str(user.user_id)  # Simplified token
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.post("/personalities")
async def create_personality(
    personality_data: PersonalityCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Create a new personality profile"""
    try:
        new_personality = PersonalityProfile(
            user_id=current_user.user_id,
            target_name=personality_data.target_name,
            description=personality_data.description
        )
        
        db.add(new_personality)
        await db.commit()
        await db.refresh(new_personality)
        
        logger.info(
            "Personality profile created",
            personality_id=str(new_personality.profile_id),
            target_name=personality_data.target_name
        )
        
        return {
            "message": "Personality profile created",
            "personality_id": str(new_personality.profile_id),
            "target_name": personality_data.target_name,
            "completion_percentage": 0.0
        }
        
    except Exception as e:
        logger.error("Personality creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create personality profile"
        )

@app.get("/personalities")
async def list_personalities(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """List user's personality profiles"""
    try:
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.user_id == current_user.user_id)
        )
        personalities = result.scalars().all()
        
        return [
            {
                "personality_id": str(p.profile_id),
                "target_name": p.target_name,
                "description": p.description,
                "completion_percentage": p.completion_percentage,
                "created_at": p.created_at.isoformat() if p.created_at else None
            }
            for p in personalities
        ]
        
    except Exception as e:
        logger.error("Failed to list personalities", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve personality profiles"
        )

@app.get("/personalities/{personality_id}")
async def get_personality_detail(
    personality_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """获取单个人格档案的完整详情"""
    try:
        result = await db.execute(
            select(PersonalityProfile)
            .where(
                PersonalityProfile.profile_id == personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
            .options(
                selectinload(PersonalityProfile.events),
                selectinload(PersonalityProfile.beliefs),
                selectinload(PersonalityProfile.entities),
                selectinload(PersonalityProfile.family_members)
            )
        )
        profile = result.scalar_one_or_none()

        if not profile:
            raise HTTPException(status_code=404, detail="Personality profile not found")

        # 将数据格式化为前端需要的结构
        return {
            "profile_id": str(profile.profile_id),
            "target_name": profile.target_name,
            "description": profile.description,
            "big_five": {
                "openness": profile.openness_score or 0.5,
                "conscientiousness": profile.conscientiousness_score or 0.5,
                "extraversion": profile.extraversion_score or 0.5,
                "agreeableness": profile.agreeableness_score or 0.5,
                "neuroticism": profile.neuroticism_score or 0.5,
            },
            "attachment_style": profile.attachment_style,
            "cultural_background": profile.cultural_background,
            "events": [
                {"title": e.title, "age": e.age_at_event, "narrative": e.full_narrative}
                for e in profile.events
            ],
            "beliefs": [
                {"statement": b.statement, "explanation": b.full_explanation}
                for b in profile.beliefs
            ],
            "family_members": [
                {"relationship": fm.relationship_type, "summary": fm.personality_summary}
                for fm in profile.family_members
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get personality detail", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve profile details")

@app.post("/chat/start/{personality_id}")
async def start_conversation(
    personality_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Start a new conversation for personality analysis"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # Create new conversation
        conversation = Conversation(
            user_id=current_user.user_id,
            personality_id=personality.profile_id
        )
        
        db.add(conversation)
        await db.commit()
        await db.refresh(conversation)
        
        # THINK phase: Generate initial questioning strategy
        strategy = await socratic_cycle.think_phase(personality_id, db)
        
        # ASK phase: Generate first question
        question = await socratic_cycle.ask_phase(strategy, [])
        
        # Save AI message
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=question.question_text,
            analysis_data={"question_context": question.question_context}
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Conversation started",
            conversation_id=str(conversation.conversation_id),
            personality_id=personality_id
        )
        
        return {
            "conversation_id": str(conversation.conversation_id),
            "ai_response": question.question_text,
            "question_context": question.question_context
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start conversation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start conversation"
        )

@app.post("/chat/respond")
async def respond_to_user(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Process user response and continue conversation"""
    try:
        if not request.conversation_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Conversation ID is required"
            )
        
        # Get conversation
        result = await db.execute(
            select(Conversation).where(
                Conversation.conversation_id == request.conversation_id,
                Conversation.user_id == current_user.user_id
            )
        )
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Save user message
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.user_input
        )
        
        db.add(user_message)
        await db.commit()
        
        # Get conversation history
        history_result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation.conversation_id)
            .order_by(Message.timestamp)
        )
        messages = history_result.scalars().all()
        conversation_history = [f"{msg.sender}: {msg.content}" for msg in messages]
        
        # ANALYZE phase: Process user input
        analysis = await socratic_cycle.analyze_phase(
            user_input=request.user_input,
            personality_id=str(conversation.personality_id),
            conversation_history=conversation_history,
            db=db
        )
        
        # THINK phase: Plan next question
        strategy = await socratic_cycle.think_phase(str(conversation.personality_id), db)
        
        # ASK phase: Generate next question
        next_question = await socratic_cycle.ask_phase(strategy, conversation_history)
        
        # Save AI response
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=next_question.question_text,
            analysis_data={
                "analysis_summary": analysis.psychological_summary,
                "confidence_score": analysis.confidence_score,
                "question_context": next_question.question_context
            }
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "User response processed",
            conversation_id=request.conversation_id,
            confidence_score=analysis.confidence_score
        )
        
        return {
            "ai_response": next_question.question_text,
            "analysis_summary": analysis.psychological_summary,
            "confidence_score": analysis.confidence_score,
            "question_context": next_question.question_context
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to process user response", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process response"
        )

@app.post("/predict")
async def predict_response(
    request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Predict how the cloned personality would respond to a situation"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == request.personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # PREDICT phase: Generate prediction
        prediction = await socratic_cycle.predict_phase(
            personality_id=request.personality_id,
            situation=request.situation,
            db=db
        )
        
        logger.info(
            "Prediction generated",
            personality_id=request.personality_id,
            confidence=prediction.confidence
        )
        
        return {
            "predicted_response": prediction.predicted_response,
            "confidence": prediction.confidence,
            "reasoning": prediction.reasoning,
            "alternative_responses": prediction.alternative_responses
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Prediction failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate prediction"
        )

@app.post("/validate")
async def validate_personality(
    request: ValidationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db_session)
):
    """Validate personality cloning accuracy"""
    try:
        # Verify personality belongs to user
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == request.personality_id,
                PersonalityProfile.user_id == current_user.user_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # VALIDATE phase: Assess similarity
        similarity = await socratic_cycle.validate_phase(
            personality_id=request.personality_id,
            reference_data=request.reference_data,
            db=db
        )
        
        logger.info(
            "Validation completed",
            personality_id=request.personality_id,
            overall_similarity=similarity.overall_similarity
        )
        
        return {
            "overall_similarity": similarity.overall_similarity,
            "dimension_similarities": similarity.dimension_similarities,
            "behavioral_similarity": similarity.behavioral_similarity,
            "communication_similarity": similarity.communication_similarity,
            "value_alignment": similarity.value_alignment,
            "strengths": similarity.strengths,
            "gaps": similarity.gaps,
            "improvement_suggestions": similarity.improvement_suggestions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Validation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate personality"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
</file>

<file path="backend/requirements-core.txt">
# Core dependencies - essential packages only, no compilation required

# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database (basic)
sqlalchemy[asyncio]>=2.0.0
aiosqlite>=0.19.0

# AI/ML (core, no compilation)
google-generativeai>=0.8.0
instructor>=1.6.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Utilities
python-dotenv>=1.0.0
httpx>=0.25.0
python-dateutil>=2.8.0

# Logging
structlog>=23.2.0

# Basic data processing (no compilation)
requests>=2.31.0
typing-extensions>=4.8.0
</file>

<file path="backend/requirements-minimal.txt">
# 最小化依赖 - 仅核心功能
# 如果完整安装失败，可以使用这个文件

# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database (基础)
sqlalchemy[asyncio]>=2.0.0
aiosqlite>=0.19.0

# AI/ML (核心)
google-generativeai>=0.8.0
instructor>=1.6.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Utilities
python-dotenv>=1.0.0
httpx>=0.25.0
python-dateutil>=2.8.0

# Logging
structlog>=23.2.0
</file>

<file path="backend/requirements.txt">
# Core framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
aiosqlite==0.19.0
alembic==1.13.1

# Graph database
neo4j==5.15.0

# Vector database
chromadb==0.4.18

# Cache
redis==5.0.1

# Search
elasticsearch==8.11.0

# AI/ML
google-generativeai==0.8.0
openai==1.54.0
instructor==1.6.0
transformers==4.46.0
torch>=2.6.0
sentence-transformers==3.3.0
numpy>=1.24.0

# Text analysis
textstat==0.7.3
spacy==3.7.2
nltk==3.8.1

# Audio analysis (optional)
azure-cognitiveservices-speech==1.34.0
librosa==0.10.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Utilities
python-dotenv==1.0.0
httpx==0.25.2
aiofiles==23.2.1
python-dateutil==2.8.2

# Monitoring and logging
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
mypy==1.7.1
</file>

<file path="backend/simple_main.py">
"""
简化的FastAPI应用 - 仅核心功能
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="人格复刻系统 - 简化版",
    description="基本功能演示",
    version="1.0.0-simple"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 简单的数据模型
class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class ChatRequest(BaseModel):
    user_input: str

# 模拟数据存储
users_db = {}
conversations_db = {}

@app.get("/")
async def root():
    """健康检查"""
    return {
        "message": "人格复刻系统简化版正在运行",
        "version": "1.0.0-simple",
        "status": "healthy"
    }

@app.post("/auth/register")
async def register_user(user_data: UserCreate):
    """用户注册"""
    if user_data.username in users_db:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    user_id = f"user_{len(users_db) + 1}"
    users_db[user_data.username] = {
        "user_id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "password": user_data.password  # 简化版，实际应该加密
    }
    
    return {
        "message": "注册成功",
        "user_id": user_id,
        "token": user_id
    }

@app.post("/auth/login")
async def login_user(login_data: UserLogin):
    """用户登录"""
    if login_data.username not in users_db:
        raise HTTPException(status_code=401, detail="用户不存在")
    
    user = users_db[login_data.username]
    if user["password"] != login_data.password:
        raise HTTPException(status_code=401, detail="密码错误")
    
    return {
        "message": "登录成功",
        "user_id": user["user_id"],
        "token": user["user_id"]
    }

@app.post("/personalities")
async def create_personality():
    """创建人格档案"""
    personality_id = f"personality_{len(conversations_db) + 1}"
    conversations_db[personality_id] = {
        "personality_id": personality_id,
        "target_name": "演示人格",
        "completion_percentage": 0.0,
        "messages": []
    }
    
    return {
        "message": "人格档案创建成功",
        "personality_id": personality_id,
        "target_name": "演示人格",
        "completion_percentage": 0.0
    }

@app.get("/personalities")
async def list_personalities():
    """列出人格档案"""
    return [
        {
            "personality_id": p["personality_id"],
            "target_name": p["target_name"],
            "completion_percentage": p["completion_percentage"]
        }
        for p in conversations_db.values()
    ]

@app.post("/chat/start/{personality_id}")
async def start_conversation(personality_id: str):
    """开始对话"""
    if personality_id not in conversations_db:
        raise HTTPException(status_code=404, detail="人格档案不存在")
    
    conversation_id = f"conv_{personality_id}_{len(conversations_db[personality_id]['messages']) + 1}"
    
    # 简化的AI响应
    ai_response = "你好！我是人格分析助手。请告诉我关于这个人的一些基本信息，比如他们的性格特点、兴趣爱好或者一些印象深刻的事情。"
    
    conversations_db[personality_id]["messages"].append({
        "sender": "ai",
        "content": ai_response,
        "timestamp": "now"
    })
    
    return {
        "conversation_id": conversation_id,
        "ai_response": ai_response,
        "question_context": "基本信息收集"
    }

@app.post("/chat/respond")
async def respond_to_user(request: ChatRequest):
    """响应用户输入"""
    # 简化的AI响应逻辑
    user_input = request.user_input.lower()
    
    if "性格" in user_input or "特点" in user_input:
        ai_response = "很有趣！你能具体描述一下这些性格特点在日常生活中是如何体现的吗？比如在面对压力或做决定时的表现。"
    elif "兴趣" in user_input or "爱好" in user_input:
        ai_response = "这些兴趣爱好很能反映一个人的内在特质。你觉得这些爱好对他/她的人生观有什么影响吗？"
    elif "工作" in user_input or "职业" in user_input:
        ai_response = "工作环境往往能展现一个人的价值观和处事方式。你能分享一些他/她在工作中的具体表现或故事吗？"
    else:
        ai_response = "谢谢你的分享！这些信息很有价值。你还能告诉我更多关于他/她的情感表达方式或者人际关系处理风格吗？"
    
    return {
        "ai_response": ai_response,
        "analysis_summary": "正在分析用户提供的信息...",
        "confidence_score": 0.75,
        "question_context": "深度探索"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
</file>

<file path="backend/sql/init.sql">
-- 100% 人格复刻系统数据库初始化脚本
-- 创建必要的扩展和初始数据

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用全文搜索扩展
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建枚举类型
DO $$ BEGIN
    CREATE TYPE personality_dimension AS ENUM (
        'openness', 'conscientiousness', 'extraversion', 
        'agreeableness', 'neuroticism'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE cognitive_style AS ENUM (
        'analytical', 'intuitive', 'systematic', 'creative'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE emotional_state AS ENUM (
        'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'neutral'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建索引函数（在表创建后执行）
CREATE OR REPLACE FUNCTION create_personality_indexes() RETURNS void AS $$
BEGIN
    -- 用户表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_username') THEN
        CREATE INDEX idx_users_username ON users(username);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_email') THEN
        CREATE INDEX idx_users_email ON users(email);
    END IF;

    -- 人格档案表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_user_id') THEN
        CREATE INDEX idx_personality_profiles_user_id ON personality_profiles(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_target_name') THEN
        CREATE INDEX idx_personality_profiles_target_name ON personality_profiles(target_name);
    END IF;

    -- 实体表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_personality_id') THEN
        CREATE INDEX idx_entities_personality_id ON entities(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_name') THEN
        CREATE INDEX idx_entities_name ON entities(name);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_type') THEN
        CREATE INDEX idx_entities_entity_type ON entities(entity_type);
    END IF;

    -- 信念表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_personality_id') THEN
        CREATE INDEX idx_beliefs_personality_id ON beliefs(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_category') THEN
        CREATE INDEX idx_beliefs_category ON beliefs(belief_category);
    END IF;

    -- 事件表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_personality_id') THEN
        CREATE INDEX idx_events_personality_id ON events(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_age') THEN
        CREATE INDEX idx_events_age ON events(age_at_event);
    END IF;

    -- 对话表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_user_id') THEN
        CREATE INDEX idx_conversations_user_id ON conversations(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_personality_id') THEN
        CREATE INDEX idx_conversations_personality_id ON conversations(personality_id);
    END IF;

    -- 消息表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_conversation_id') THEN
        CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_timestamp') THEN
        CREATE INDEX idx_messages_timestamp ON messages(timestamp);
    END IF;

    -- 全文搜索索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_statement_gin') THEN
        CREATE INDEX idx_beliefs_statement_gin ON beliefs USING gin(statement gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_narrative_gin') THEN
        CREATE INDEX idx_events_narrative_gin ON events USING gin(full_narrative gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_content_gin') THEN
        CREATE INDEX idx_messages_content_gin ON messages USING gin(content gin_trgm_ops);
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 创建触发器函数用于更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建用于计算人格完成度的函数
CREATE OR REPLACE FUNCTION calculate_personality_completion(profile_id UUID)
RETURNS FLOAT AS $$
DECLARE
    completion_score FLOAT := 0;
    entity_count INTEGER;
    belief_count INTEGER;
    event_count INTEGER;
    cognitive_pattern_count INTEGER;
BEGIN
    -- 计算各个维度的完成度
    
    -- 基础信息 (20%)
    SELECT CASE 
        WHEN target_name IS NOT NULL AND target_name != '' THEN 0.2 
        ELSE 0 
    END INTO completion_score
    FROM personality_profiles 
    WHERE profile_id = calculate_personality_completion.profile_id;
    
    -- 实体关系 (25%)
    SELECT COUNT(*) INTO entity_count
    FROM entities 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(entity_count * 0.05, 0.25);
    
    -- 信念系统 (25%)
    SELECT COUNT(*) INTO belief_count
    FROM beliefs 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(belief_count * 0.08, 0.25);
    
    -- 生活事件 (20%)
    SELECT COUNT(*) INTO event_count
    FROM events 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(event_count * 0.04, 0.20);
    
    -- 认知模式 (10%)
    SELECT COUNT(*) INTO cognitive_pattern_count
    FROM cognitive_patterns 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(cognitive_pattern_count * 0.02, 0.10);
    
    RETURN LEAST(completion_score * 100, 100);
END;
$$ LANGUAGE plpgsql;

-- 创建用于搜索相似人格的函数
CREATE OR REPLACE FUNCTION find_similar_personalities(
    target_profile_id UUID,
    similarity_threshold FLOAT DEFAULT 0.7
)
RETURNS TABLE(
    profile_id UUID,
    target_name VARCHAR,
    similarity_score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.profile_id,
        p.target_name,
        (
            1.0 - (
                ABS(p.openness_score - tp.openness_score) +
                ABS(p.conscientiousness_score - tp.conscientiousness_score) +
                ABS(p.extraversion_score - tp.extraversion_score) +
                ABS(p.agreeableness_score - tp.agreeableness_score) +
                ABS(p.neuroticism_score - tp.neuroticism_score)
            ) / 5.0
        ) as similarity_score
    FROM personality_profiles p
    CROSS JOIN personality_profiles tp
    WHERE tp.profile_id = target_profile_id
    AND p.profile_id != target_profile_id
    AND (
        1.0 - (
            ABS(p.openness_score - tp.openness_score) +
            ABS(p.conscientiousness_score - tp.conscientiousness_score) +
            ABS(p.extraversion_score - tp.extraversion_score) +
            ABS(p.agreeableness_score - tp.agreeableness_score) +
            ABS(p.neuroticism_score - tp.neuroticism_score)
        ) / 5.0
    ) >= similarity_threshold
    ORDER BY similarity_score DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建演示数据插入函数
CREATE OR REPLACE FUNCTION insert_demo_data() RETURNS void AS $$
DECLARE
    demo_user_id UUID;
    demo_personality_id UUID;
BEGIN
    -- 检查是否已存在演示数据
    IF EXISTS (SELECT 1 FROM users WHERE username = 'demo') THEN
        RETURN;
    END IF;

    -- 创建演示用户
    INSERT INTO users (username, email, hashed_password)
    VALUES ('demo', '<EMAIL>', 'demo123')
    RETURNING user_id INTO demo_user_id;

    -- 创建演示人格档案
    INSERT INTO personality_profiles (
        user_id, target_name, description,
        openness_score, conscientiousness_score, extraversion_score,
        agreeableness_score, neuroticism_score,
        decision_making_speed, risk_tolerance,
        average_response_length, vocabulary_complexity, emotional_expressiveness
    )
    VALUES (
        demo_user_id, '演示人格', '这是一个演示用的人格档案',
        0.7, 0.6, 0.8, 0.9, 0.3,
        0.6, 0.4,
        150.0, 0.7, 0.8
    )
    RETURNING profile_id INTO demo_personality_id;

    -- 插入一些演示实体
    INSERT INTO entities (personality_id, name, entity_type, relationship_type, emotional_valence, importance_score)
    VALUES 
        (demo_personality_id, '家人', 'person', 'family', 0.9, 0.95),
        (demo_personality_id, '朋友', 'person', 'friend', 0.8, 0.8),
        (demo_personality_id, '工作', 'concept', 'professional', 0.6, 0.7);

    -- 插入一些演示信念
    INSERT INTO beliefs (personality_id, statement, belief_category, conviction_strength, flexibility_score)
    VALUES 
        (demo_personality_id, '诚实是最重要的品质', 'moral', 0.9, 0.2),
        (demo_personality_id, '努力工作会有回报', 'personal', 0.8, 0.4),
        (demo_personality_id, '家庭比事业更重要', 'personal', 0.7, 0.6);

    -- 插入一些演示事件
    INSERT INTO events (personality_id, title, age_at_event, life_stage, event_type, emotional_impact, centrality_score, memory_vividness)
    VALUES 
        (demo_personality_id, '大学毕业', 22, 'young_adult', 'achievement', 0.8, 0.7, 0.9),
        (demo_personality_id, '第一份工作', 23, 'young_adult', 'professional', 0.6, 0.6, 0.8),
        (demo_personality_id, '结婚', 28, 'adult', 'relationship', 0.9, 0.9, 0.95);

END;
$$ LANGUAGE plpgsql;

-- 数据库初始化完成后的通知
DO $$
BEGIN
    RAISE NOTICE '人格复刻系统数据库初始化完成';
    RAISE NOTICE '- 已创建必要的扩展和类型';
    RAISE NOTICE '- 已定义索引创建函数';
    RAISE NOTICE '- 已创建工具函数';
    RAISE NOTICE '- 准备插入演示数据';
END $$;
</file>

<file path="check_docker.py">
#!/usr/bin/env python3
"""
Docker状态检查和修复脚本
"""

import subprocess
import time
import sys
import os

def check_docker_installed():
    """检查Docker是否安装"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker未正确安装")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装，请先安装Docker Desktop")
        return False

def check_docker_running():
    """检查Docker是否运行"""
    try:
        result = subprocess.run(['docker', 'info'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker服务正在运行")
            return True
        else:
            print("❌ Docker服务未运行")
            print(f"错误信息: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 检查Docker状态时出错: {e}")
        return False

def start_docker_desktop():
    """尝试启动Docker Desktop"""
    print("🔄 尝试启动Docker Desktop...")
    
    if os.name == 'nt':  # Windows
        try:
            # 尝试启动Docker Desktop
            subprocess.Popen([
                'C:\\Program Files\\Docker\\Docker\\Docker Desktop.exe'
            ], shell=True)
            print("✅ 已尝试启动Docker Desktop")
            return True
        except Exception as e:
            print(f"❌ 启动Docker Desktop失败: {e}")
            return False
    else:
        print("💡 请手动启动Docker Desktop应用")
        return False

def wait_for_docker():
    """等待Docker启动"""
    print("⏳ 等待Docker启动...")
    
    max_wait = 60  # 最多等待60秒
    wait_time = 0
    
    while wait_time < max_wait:
        if check_docker_running():
            return True
        
        print(f"   等待中... ({wait_time}/{max_wait}秒)")
        time.sleep(5)
        wait_time += 5
    
    print("❌ Docker启动超时")
    return False

def check_docker_compose():
    """检查docker-compose是否可用"""
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker Compose可用: {result.stdout.strip()}")
            return True
        else:
            # 尝试使用docker compose (新版本)
            result = subprocess.run(['docker', 'compose', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Docker Compose可用: {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker Compose不可用")
                return False
    except FileNotFoundError:
        print("❌ Docker Compose未安装")
        return False

def test_docker_functionality():
    """测试Docker基本功能"""
    print("🧪 测试Docker基本功能...")
    
    try:
        # 运行hello-world容器
        result = subprocess.run([
            'docker', 'run', '--rm', 'hello-world'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Docker功能测试通过")
            return True
        else:
            print(f"❌ Docker功能测试失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker功能测试超时")
        return False
    except Exception as e:
        print(f"❌ Docker功能测试出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🐳 Docker状态检查和修复")
    print("=" * 50)
    print()
    
    # 1. 检查Docker是否安装
    if not check_docker_installed():
        print("\n💡 解决方案:")
        print("1. 下载并安装Docker Desktop:")
        print("   https://www.docker.com/products/docker-desktop/")
        print("2. 安装完成后重新运行此脚本")
        return
    
    print()
    
    # 2. 检查Docker是否运行
    if not check_docker_running():
        print("\n🔄 尝试修复Docker服务...")
        
        # 尝试启动Docker Desktop
        if start_docker_desktop():
            # 等待Docker启动
            if wait_for_docker():
                print("✅ Docker服务已启动")
            else:
                print("\n💡 手动解决方案:")
                print("1. 手动启动Docker Desktop应用")
                print("2. 等待Docker完全启动（系统托盘图标变绿）")
                print("3. 重新运行此脚本")
                return
        else:
            print("\n💡 手动解决方案:")
            print("1. 手动启动Docker Desktop应用")
            print("2. 如果启动失败，尝试重启计算机")
            print("3. 检查Windows功能中的Hyper-V是否启用")
            return
    
    print()
    
    # 3. 检查Docker Compose
    if not check_docker_compose():
        print("\n💡 Docker Compose解决方案:")
        print("1. 更新Docker Desktop到最新版本")
        print("2. 或使用 'docker compose' 命令替代 'docker-compose'")
        return
    
    print()
    
    # 4. 测试Docker功能
    if test_docker_functionality():
        print("\n🎉 Docker环境完全正常！")
        print("\n🚀 现在可以启动项目服务:")
        print("   docker-compose up -d")
        print("   或运行: python quick_start.py")
    else:
        print("\n⚠️  Docker环境有问题，建议:")
        print("1. 重启Docker Desktop")
        print("2. 重启计算机")
        print("3. 重新安装Docker Desktop")

if __name__ == "__main__":
    main()
</file>

<file path="check_project.py">
#!/usr/bin/env python3
"""
项目完整性检查脚本
"""

import os
from pathlib import Path

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ {file_path} {description}")
        return True
    else:
        print(f"❌ {file_path} {description} - 文件缺失")
        return False

def check_directory_exists(dir_path, description=""):
    """检查目录是否存在"""
    if Path(dir_path).is_dir():
        print(f"✅ {dir_path}/ {description}")
        return True
    else:
        print(f"❌ {dir_path}/ {description} - 目录缺失")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 100% 人格复刻系统 - 项目完整性检查")
    print("=" * 60)
    print()
    
    total_checks = 0
    passed_checks = 0
    
    # 根目录文件
    print("📁 根目录文件:")
    files_to_check = [
        ("README.md", "项目说明文档"),
        ("docker-compose.yml", "Docker服务配置"),
        ("quick_start.py", "快速启动脚本"),
        ("stop_services.py", "停止服务脚本"),
        ("test_system.py", "系统测试脚本"),
        ("check_project.py", "项目检查脚本"),
        ("DEPLOYMENT_CHECKLIST.md", "部署检查清单"),
        ("start.bat", "Windows启动脚本"),
        ("start.sh", "Linux/macOS启动脚本"),
    ]
    
    for file_path, description in files_to_check:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 后端目录结构
    print("🔧 后端目录结构:")
    backend_files = [
        ("backend/main.py", "FastAPI应用入口"),
        ("backend/requirements.txt", "Python依赖"),
        ("backend/init_db.py", "数据库初始化脚本"),
        ("backend/.env.example", "环境变量示例"),
        ("backend/app/__init__.py", "应用包初始化"),
        ("backend/app/database/__init__.py", "数据库模块"),
        ("backend/app/database/models.py", "数据库模型"),
        ("backend/app/database/db_session.py", "数据库会话管理"),
        ("backend/app/llm/__init__.py", "LLM模块"),
        ("backend/app/llm/schemas.py", "Pydantic模型"),
        ("backend/app/llm/prompts.py", "提示词模板"),
        ("backend/app/services/__init__.py", "服务模块"),
        ("backend/app/services/personality_analyzer.py", "人格分析服务"),
        ("backend/app/services/enhanced_socratic_cycle.py", "增强苏格拉底循环"),
        ("backend/sql/init.sql", "SQL初始化脚本"),
    ]
    
    for file_path, description in backend_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端目录结构
    print("🎨 前端目录结构:")
    frontend_files = [
        ("frontend/package.json", "Node.js项目配置"),
        ("frontend/vite.config.js", "Vite构建配置"),
        ("frontend/index.html", "HTML入口文件"),
        ("frontend/.env.example", "前端环境变量示例"),
        ("frontend/src/main.js", "Vue应用入口"),
        ("frontend/src/App.vue", "Vue根组件"),
        ("frontend/src/router/index.js", "路由配置"),
        ("frontend/src/stores/auth.js", "认证状态管理"),
        ("frontend/src/stores/app.js", "应用状态管理"),
        ("frontend/src/utils/api.js", "API工具类"),
    ]
    
    for file_path, description in frontend_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端组件
    print("🧩 前端组件:")
    component_files = [
        ("frontend/src/components/layout/AppHeader.vue", "应用头部组件"),
        ("frontend/src/components/layout/AppSidebar.vue", "侧边栏组件"),
        ("frontend/src/components/common/GlobalNotifications.vue", "全局通知组件"),
    ]
    
    for file_path, description in component_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端页面
    print("📄 前端页面:")
    page_files = [
        ("frontend/src/views/Dashboard.vue", "仪表板页面"),
        ("frontend/src/views/auth/Login.vue", "登录页面"),
        ("frontend/src/views/auth/Register.vue", "注册页面"),
        ("frontend/src/views/personality/PersonalityList.vue", "人格列表页面"),
        ("frontend/src/views/personality/PersonalityDetail.vue", "人格详情页面"),
        ("frontend/src/views/personality/PersonalityCreate.vue", "创建人格页面"),
        ("frontend/src/views/chat/ChatInterface.vue", "对话界面"),
        ("frontend/src/views/prediction/PredictionLab.vue", "预测实验室"),
        ("frontend/src/views/validation/ValidationCenter.vue", "验证中心"),
        ("frontend/src/views/analytics/Analytics.vue", "数据分析页面"),
        ("frontend/src/views/settings/Settings.vue", "设置页面"),
        ("frontend/src/views/error/NotFound.vue", "404页面"),
    ]
    
    for file_path, description in page_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 目录结构检查
    print("📂 目录结构:")
    directories = [
        ("backend", "后端目录"),
        ("backend/app", "应用目录"),
        ("backend/app/database", "数据库模块"),
        ("backend/app/llm", "LLM模块"),
        ("backend/app/services", "服务模块"),
        ("backend/sql", "SQL脚本目录"),
        ("frontend", "前端目录"),
        ("frontend/src", "前端源码"),
        ("frontend/src/components", "组件目录"),
        ("frontend/src/components/layout", "布局组件"),
        ("frontend/src/components/common", "通用组件"),
        ("frontend/src/views", "页面目录"),
        ("frontend/src/views/auth", "认证页面"),
        ("frontend/src/views/personality", "人格页面"),
        ("frontend/src/views/chat", "对话页面"),
        ("frontend/src/views/prediction", "预测页面"),
        ("frontend/src/views/validation", "验证页面"),
        ("frontend/src/views/analytics", "分析页面"),
        ("frontend/src/views/settings", "设置页面"),
        ("frontend/src/views/error", "错误页面"),
        ("frontend/src/stores", "状态管理"),
        ("frontend/src/utils", "工具函数"),
        ("frontend/src/router", "路由配置"),
    ]
    
    for dir_path, description in directories:
        total_checks += 1
        if check_directory_exists(dir_path, description):
            passed_checks += 1
    
    print()
    
    # 检查结果
    print("=" * 60)
    print("📊 检查结果统计")
    print("=" * 60)
    
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"完整度: {success_rate:.1f}%")
    
    print()
    
    if success_rate >= 95:
        print("🎉 项目结构完整，可以开始部署！")
        print()
        print("🚀 下一步操作:")
        print("  1. 运行 python quick_start.py 快速启动")
        print("  2. 或者按照 DEPLOYMENT_CHECKLIST.md 手动部署")
        print("  3. 运行 python test_system.py 测试系统")
    elif success_rate >= 80:
        print("⚠️  项目基本完整，但有一些文件缺失")
        print("建议检查缺失的文件后再进行部署")
    else:
        print("❌ 项目结构不完整，请检查缺失的文件")
        print("建议重新下载或克隆项目")
    
    print()
    print("📖 详细部署说明请查看:")
    print("  - README.md")
    print("  - DEPLOYMENT_CHECKLIST.md")

if __name__ == "__main__":
    main()
</file>

<file path="docker-compose.yml">
services:
  postgres:
    image: postgres:16
    container_name: personality_clone_postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: personality_clone_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # 暂时注释掉以下服务，聚焦核心功能
  # 当需要时可以逐一开启

  # neo4j:
  #   image: neo4j:5
  #   container_name: personality_clone_neo4j
  #   environment:
  #     NEO4J_AUTH: neo4j/password
  #     NEO4J_PLUGINS: '["apoc"]'
  #   ports:
  #     - "7474:7474"  # Neo4j Browser
  #     - "7687:7687"  # Bolt protocol
  #   volumes:
  #     - neo4j_data:/data
  #     - neo4j_logs:/logs
  #   restart: unless-stopped

  # chromadb:
  #   image: chromadb/chroma:latest
  #   container_name: personality_clone_chroma
  #   ports:
  #     - "8001:8000"
  #   volumes:
  #     - chroma_data:/chroma/chroma
  #   environment:
  #     - CHROMA_SERVER_HOST=0.0.0.0
  #     - CHROMA_SERVER_HTTP_PORT=8000
  #   restart: unless-stopped

  # redis:
  #   image: redis:7-alpine
  #   container_name: personality_clone_redis
  #   ports:
  #     - "6500:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
  #   container_name: personality_clone_elasticsearch
  #   environment:
  #     - discovery.type=single-node
  #     - xpack.security.enabled=false
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ports:
  #     - "9200:9200"
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   restart: unless-stopped

volumes:
  postgres_data:
  # 暂时注释掉未使用的数据卷
  # neo4j_data:
  # neo4j_logs:
  # chroma_data:
  # redis_data:
  # elasticsearch_data:
</file>

<file path="fix_ports.py">
#!/usr/bin/env python3
"""
端口冲突检查和修复脚本
"""

import socket
import subprocess
import sys
import os

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def find_free_port(start_port, host='localhost'):
    """找到一个可用的端口"""
    port = start_port
    while port < start_port + 100:
        if not check_port(host, port):
            return port
        port += 1
    return None

def kill_process_on_port(port):
    """终止占用端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True
            )
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) > 4:
                        pid = parts[-1]
                        try:
                            subprocess.run(['taskkill', '/F', '/PID', pid], 
                                         capture_output=True)
                            print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                            return True
                        except:
                            pass
        else:  # Unix/Linux/macOS
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'], 
                capture_output=True, 
                text=True
            )
            if result.stdout.strip():
                pid = result.stdout.strip()
                try:
                    subprocess.run(['kill', '-9', pid], capture_output=True)
                    print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                    return True
                except:
                    pass
    except:
        pass
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 端口冲突检查和修复")
    print("=" * 50)
    print()
    
    # 检查的端口列表
    ports_to_check = {
        5432: "PostgreSQL",
        7474: "Neo4j HTTP",
        7687: "Neo4j Bolt", 
        8001: "ChromaDB",
        6380: "Redis",
        9200: "Elasticsearch"
    }
    
    conflicts = []
    
    print("🔍 检查端口占用情况...")
    for port, service in ports_to_check.items():
        if check_port('localhost', port):
            print(f"❌ 端口 {port} ({service}) 被占用")
            conflicts.append((port, service))
        else:
            print(f"✅ 端口 {port} ({service}) 可用")
    
    print()
    
    if not conflicts:
        print("🎉 所有端口都可用，可以启动服务！")
        return
    
    print(f"⚠️  发现 {len(conflicts)} 个端口冲突")
    print()
    
    # 询问用户是否要自动修复
    response = input("是否要自动修复端口冲突？(y/n): ").lower().strip()
    
    if response == 'y':
        print("\n🔄 开始修复端口冲突...")
        
        for port, service in conflicts:
            print(f"\n处理 {service} (端口 {port})...")
            
            # 尝试终止占用进程
            if kill_process_on_port(port):
                continue
            
            # 如果无法终止，寻找替代端口
            new_port = find_free_port(port + 1)
            if new_port:
                print(f"💡 建议将 {service} 端口改为 {new_port}")
                
                # 这里可以添加自动修改配置文件的逻辑
                if service == "Redis":
                    print(f"   请手动修改 docker-compose.yml 中 Redis 的端口映射为 {new_port}:6379")
                elif service == "ChromaDB":
                    print(f"   请手动修改 docker-compose.yml 中 ChromaDB 的端口映射为 {new_port}:8000")
            else:
                print(f"❌ 无法找到 {service} 的替代端口")
    
    print()
    print("🔧 手动解决方案:")
    print("1. 停止占用端口的其他服务")
    print("2. 修改 docker-compose.yml 中的端口映射")
    print("3. 重启计算机释放所有端口")
    print()
    print("📝 常见端口冲突解决:")
    print("- Redis (6379): 可能与本地Redis冲突")
    print("- PostgreSQL (5432): 可能与本地PostgreSQL冲突") 
    print("- ChromaDB (8000): 可能与其他Web服务冲突")
    print()
    print("💡 建议使用以下命令重新启动:")
    print("   docker-compose down")
    print("   docker-compose up -d")

if __name__ == "__main__":
    main()
</file>

<file path="frontend/.env.example">
# 前端环境变量配置

# API基础URL
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_TITLE=人格复刻系统
VITE_APP_VERSION=1.0.0

# 开发配置
VITE_DEV_MODE=true
VITE_ENABLE_MOCK=false

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true
</file>

<file path="frontend/index.html">
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>100% 人格复刻系统</title>
    <meta name="description" content="基于AI的完整人格复刻与分析系统" />
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
                     'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
                     'Noto Color Emoji';
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载状态 -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载人格复刻系统...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
</file>

<file path="frontend/package.json">
{
  "name": "personality-clone-frontend",
  "version": "1.0.0",
  "description": "Frontend for 100% Personality Cloning System",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "@element-plus/icons-vue": "^2.1.0",
    "axios": "^1.6.2",
    "d3": "^7.8.5",
    "dayjs": "^1.11.10",
    "echarts": "^5.6.0",
    "element-plus": "^2.4.4",
    "highlight.js": "^11.9.0",
    "marked": "^9.1.6",
    "pinia": "^2.1.7",
    "vue": "^3.3.8",
    "vue-echarts": "^6.6.1",
    "vue-router": "^4.2.5"
  },
  "devDependencies": {
    "@types/d3": "^7.4.3",
    "@vitejs/plugin-vue": "^4.5.0",
    "eslint": "^8.54.0",
    "eslint-plugin-vue": "^9.18.1",
    "prettier": "^3.1.0",
    "vite": "^5.0.0"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
</file>

<file path="frontend/src/App.vue">
<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <!-- 全局加载遮罩 -->
      <el-loading
        v-loading="globalLoading"
        element-loading-text="正在处理中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-spinner="el-icon-loading"
      >
        <!-- 主要内容区域 -->
        <div class="app-container">
          <!-- 顶部导航栏 -->
          <AppHeader v-if="showHeader" />
          
          <!-- 侧边栏 -->
          <AppSidebar v-if="showSidebar" />
          
          <!-- 主内容区 -->
          <main class="main-content" :class="{ 'with-sidebar': showSidebar }">
            <router-view v-slot="{ Component, route }">
              <transition name="fade" mode="out-in">
                <component :is="Component" :key="route.path" />
              </transition>
            </router-view>
          </main>
          
          <!-- 全局通知 -->
          <GlobalNotifications />
        </div>
      </el-loading>
    </el-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import AppHeader from './components/layout/AppHeader.vue'
import AppSidebar from './components/layout/AppSidebar.vue'
import GlobalNotifications from './components/common/GlobalNotifications.vue'

import { useAuthStore } from './stores/auth'
import { useAppStore } from './stores/app'

// 状态管理
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()

// 响应式数据
const locale = ref(zhCn)
const globalLoading = computed(() => appStore.globalLoading)

// 计算属性
const showHeader = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideHeader
})

const showSidebar = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideSidebar
})

// 生命周期
onMounted(async () => {
  // 初始化应用
  await initializeApp()
})

// 监听路由变化
watch(route, (to) => {
  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
})

// 方法
const initializeApp = async () => {
  try {
    appStore.setGlobalLoading(true)
    
    // 检查认证状态
    await authStore.checkAuth()
    
    // 初始化应用设置
    await appStore.initializeApp()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setGlobalLoading(false)
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.main-content {
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
  padding: 20px;
}

.main-content.with-sidebar {
  margin-left: 250px;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }
  
  .main-content {
    padding: 10px;
  }
}

/* 全局样式 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

/* 自定义滚动条 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background-color: #a8a8a8;
}
</style>

<style>
/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

#app {
  height: 100%;
}

/* 自定义主题色 */
:root {
  --el-color-primary: #667eea;
  --el-color-primary-light-3: #8b9df0;
  --el-color-primary-light-5: #a6b5f3;
  --el-color-primary-light-7: #c1cdf6;
  --el-color-primary-light-8: #d1ddf8;
  --el-color-primary-light-9: #e8edfb;
  --el-color-primary-dark-2: #5a72e8;
}

/* 动画效果 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.full-width {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
</file>

<file path="frontend/src/components/common/GlobalNotifications.vue">
<template>
  <div class="global-notifications">
    <!-- 这个组件用于显示全局通知，Element Plus 的 ElMessage 会自动处理 -->
  </div>
</template>

<script setup>
// 这个组件主要用于全局通知的容器
// Element Plus 的消息组件会自动挂载到 body
</script>

<style scoped>
.global-notifications {
  /* 预留给全局通知的样式 */
}
</style>
</file>

<file path="frontend/src/components/layout/AppHeader.vue">
<template>
  <header class="app-header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon size="20"><Menu /></el-icon>
      </el-button>
      
      <div class="logo">
        <span class="logo-text">人格复刻系统</span>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 通知 -->
      <el-dropdown trigger="click" class="notification-dropdown">
        <el-button type="text" class="header-button">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon size="18"><Bell /></el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="notification-header">
              <span>通知</span>
              <el-button type="text" size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notifications.slice(0, 5)"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
                </div>
              </div>
              <div v-if="notifications.length === 0" class="no-notifications">
                暂无通知
              </div>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 主题切换 -->
      <el-button
        type="text"
        class="header-button"
        @click="toggleTheme"
      >
        <el-icon size="18">
          <component :is="isDarkTheme ? 'Sunny' : 'Moon'" />
        </el-icon>
      </el-button>
      
      <!-- 用户菜单 -->
      <el-dropdown trigger="click">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            {{ userInfo.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <span class="username">{{ userInfo.username }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="goToSettings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Menu, Bell, Sunny, Moon, ArrowDown, Setting, SwitchButton
} from '@element-plus/icons-vue'

import { useAuthStore } from '../../stores/auth'
import { useAppStore } from '../../stores/app'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const notifications = computed(() => appStore.notifications)
const unreadCount = computed(() => appStore.unreadNotifications)
const isDarkTheme = computed(() => appStore.isDarkTheme)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const markAllAsRead = () => {
  appStore.markAllNotificationsAsRead()
}

const formatTime = (time) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now - notificationTime
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const goToSettings = () => {
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await authStore.logout()
    if (result.success) {
      router.push('/login')
    }
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 15px;
  color: #606266;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-button {
  color: #606266;
  padding: 8px;
}

.header-button:hover {
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.username {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
}

/* 通知相关样式 */
.notification-dropdown :deep(.el-dropdown-menu) {
  width: 320px;
  max-height: 400px;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #2c3e50;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-content {
  width: 100%;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.no-notifications {
  padding: 40px 16px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 15px;
  }
  
  .username {
    display: none;
  }
  
  .notification-dropdown :deep(.el-dropdown-menu) {
    width: 280px;
  }
}

/* 深色模式 */
.dark .app-header {
  background: #1f2937;
  border-bottom-color: #374151;
}

.dark .logo-text {
  color: #f9fafb;
}

.dark .header-button {
  color: #d1d5db;
}

.dark .header-button:hover {
  color: #60a5fa;
}

.dark .user-info:hover {
  background-color: #374151;
}

.dark .username {
  color: #f9fafb;
}
</style>
</file>

<file path="frontend/src/components/layout/AppSidebar.vue">
<template>
  <aside class="app-sidebar" :class="{ collapsed: sidebarCollapsed }">
    <div class="sidebar-content">
      <el-menu
        :default-active="activeMenu"
        :collapse="sidebarCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
          @click="handleMenuClick(route)"
        >
          <el-icon>
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <el-button
        type="text"
        class="collapse-button"
        @click="toggleSidebar"
      >
        <el-icon>
          <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
        <span v-if="!sidebarCollapsed">收起菜单</span>
      </el-button>
    </div>
  </aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Expand, Fold } from '@element-plus/icons-vue'

import { useAppStore } from '../../stores/app'
import { menuRoutes } from '../../router'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const activeMenu = computed(() => {
  // 根据当前路由确定激活的菜单项
  const path = route.path
  
  // 精确匹配
  if (menuRoutes.find(r => r.path === path)) {
    return path
  }
  
  // 模糊匹配（用于子路由）
  for (const menuRoute of menuRoutes) {
    if (path.startsWith(menuRoute.path) && menuRoute.path !== '/') {
      return menuRoute.path
    }
  }
  
  return '/'
})

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const handleMenuClick = (route) => {
  // 可以在这里添加菜单点击的额外逻辑
  console.log('菜单点击:', route.meta.title)
}
</script>

<style scoped>
.app-sidebar {
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  width: 250px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.05);
}

.app-sidebar.collapsed {
  width: 64px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f0f9ff;
  color: #409eff;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item .el-icon) {
  margin-right: 12px;
  font-size: 18px;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item) {
  padding: 0 20px;
  text-align: center;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item .el-icon) {
  margin-right: 0;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.collapse-button {
  width: 100%;
  justify-content: flex-start;
  color: #606266;
  font-size: 14px;
}

.collapse-button:hover {
  color: #409eff;
  background-color: #f0f9ff;
}

.app-sidebar.collapsed .collapse-button {
  justify-content: center;
}

.app-sidebar.collapsed .collapse-button span {
  display: none;
}

/* 自定义滚动条 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* 移动端遮罩 */
  .app-sidebar:not(.collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

/* 深色模式 */
.dark .app-sidebar {
  background: #1f2937;
  border-right-color: #374151;
}

.dark .sidebar-menu :deep(.el-menu-item) {
  color: #d1d5db;
}

.dark .sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #374151;
  color: #60a5fa;
}

.dark .sidebar-footer {
  border-top-color: #374151;
}

.dark .collapse-button {
  color: #d1d5db;
}

.dark .collapse-button:hover {
  color: #60a5fa;
  background-color: #374151;
}
</style>
</file>

<file path="frontend/src/main.js">
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 创建应用实例
const app = createApp(App)

// 注册 Pinia 状态管理
const pinia = createPinia()
app.use(pinia)

// 注册路由
app.use(router)

// 注册 Element Plus
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 这里可以添加错误上报逻辑
}

// 全局属性
app.config.globalProperties.$ELEMENT = { size: 'default' }

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 尝试从本地存储恢复认证状态
    await authStore.checkAuth()
    
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 人格复刻系统启动成功')
  console.log('📊 当前环境:', import.meta.env.MODE)
  console.log('🔗 API地址:', import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
}
</file>

<file path="frontend/src/router/index.js">
import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Login = () => import('../views/auth/Login.vue')
const Register = () => import('../views/auth/Register.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const PersonalityList = () => import('../views/personality/PersonalityList.vue')
const PersonalityDetail = () => import('../views/personality/PersonalityDetail.vue')
const PersonalityCreate = () => import('../views/personality/PersonalityCreate.vue')
const ChatInterface = () => import('../views/chat/ChatInterface.vue')
const PredictionLab = () => import('../views/prediction/PredictionLab.vue')
const ValidationCenter = () => import('../views/validation/ValidationCenter.vue')
const Analytics = () => import('../views/analytics/Analytics.vue')
const Settings = () => import('../views/settings/Settings.vue')
const NotFound = () => import('../views/error/NotFound.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      hideHeader: true,
      hideSidebar: true,
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiresAuth: true,
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    component: PersonalityList,
    meta: {
      title: '人格档案',
      requiresAuth: true,
      icon: 'User'
    }
  },
  {
    path: '/personalities/create',
    name: 'PersonalityCreate',
    component: PersonalityCreate,
    meta: {
      title: '创建人格档案',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '创建档案' }
      ]
    }
  },
  {
    path: '/personalities/:id',
    name: 'PersonalityDetail',
    component: PersonalityDetail,
    meta: {
      title: '人格详情',
      requiresAuth: true,
      breadcrumb: [
        { title: '人格档案', to: '/personalities' },
        { title: '详情' }
      ]
    }
  },
  {
    path: '/chat/:personalityId?',
    name: 'ChatInterface',
    component: ChatInterface,
    meta: {
      title: '对话分析',
      requiresAuth: true,
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/prediction',
    name: 'PredictionLab',
    component: PredictionLab,
    meta: {
      title: '预测实验室',
      requiresAuth: true,
      icon: 'TrendCharts'
    }
  },
  {
    path: '/validation',
    name: 'ValidationCenter',
    component: ValidationCenter,
    meta: {
      title: '验证中心',
      requiresAuth: true,
      icon: 'CircleCheck'
    }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: Analytics,
    meta: {
      title: '数据分析',
      requiresAuth: true,
      icon: 'DataAnalysis'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      requiresAuth: true,
      icon: 'Setting'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      hideHeader: true,
      hideSidebar: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出菜单配置
export const menuRoutes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    meta: {
      title: '仪表板',
      icon: 'Dashboard'
    }
  },
  {
    path: '/personalities',
    name: 'PersonalityList',
    meta: {
      title: '人格档案',
      icon: 'User'
    }
  },
  {
    path: '/chat',
    name: 'ChatInterface',
    meta: {
      title: '对话分析',
      icon: 'ChatDotRound'
    }
  },
  {
    path: '/prediction',
    name: 'PredictionLab',
    meta: {
      title: '预测实验室',
      icon: 'TrendCharts'
    }
  },
  {
    path: '/validation',
    name: 'ValidationCenter',
    meta: {
      title: '验证中心',
      icon: 'CircleCheck'
    }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    meta: {
      title: '数据分析',
      icon: 'DataAnalysis'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    meta: {
      title: '系统设置',
      icon: 'Setting'
    }
  }
]
</file>

<file path="frontend/src/stores/app.js">
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const globalLoading = ref(false)
  const sidebarCollapsed = ref(false)
  const theme = ref(localStorage.getItem('theme') || 'light')
  const language = ref(localStorage.getItem('language') || 'zh-CN')
  const notifications = ref([])
  const appSettings = ref({
    autoSave: true,
    soundEnabled: true,
    animationsEnabled: true,
    compactMode: false
  })

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 动作
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML类名以应用主题
    document.documentElement.className = newTheme === 'dark' ? 'dark' : ''
  }

  const toggleTheme = () => {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }

  const setLanguage = (newLanguage) => {
    language.value = newLanguage
    localStorage.setItem('language', newLanguage)
  }

  const addNotification = (notification) => {
    const id = Date.now().toString()
    notifications.value.unshift({
      id,
      timestamp: new Date(),
      read: false,
      ...notification
    })
    
    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markNotificationAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const updateAppSettings = (newSettings) => {
    appSettings.value = { ...appSettings.value, ...newSettings }
    localStorage.setItem('appSettings', JSON.stringify(appSettings.value))
  }

  const initializeApp = async () => {
    try {
      // 恢复侧边栏状态
      const savedSidebarState = localStorage.getItem('sidebarCollapsed')
      if (savedSidebarState !== null) {
        sidebarCollapsed.value = savedSidebarState === 'true'
      }

      // 恢复应用设置
      const savedSettings = localStorage.getItem('appSettings')
      if (savedSettings) {
        appSettings.value = { ...appSettings.value, ...JSON.parse(savedSettings) }
      }

      // 应用主题
      document.documentElement.className = theme.value === 'dark' ? 'dark' : ''

      // 初始化通知（可以从服务器获取）
      // await loadNotifications()

    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  const showMessage = (message, type = 'info') => {
    addNotification({
      type,
      title: type === 'error' ? '错误' : type === 'success' ? '成功' : '提示',
      message,
      duration: type === 'error' ? 0 : 3000
    })
  }

  const showSuccess = (message) => {
    showMessage(message, 'success')
  }

  const showError = (message) => {
    showMessage(message, 'error')
  }

  const showWarning = (message) => {
    showMessage(message, 'warning')
  }

  const showInfo = (message) => {
    showMessage(message, 'info')
  }

  // 响应式断点
  const breakpoints = ref({
    xs: window.matchMedia('(max-width: 575px)'),
    sm: window.matchMedia('(min-width: 576px) and (max-width: 767px)'),
    md: window.matchMedia('(min-width: 768px) and (max-width: 991px)'),
    lg: window.matchMedia('(min-width: 992px) and (max-width: 1199px)'),
    xl: window.matchMedia('(min-width: 1200px)')
  })

  const isMobile = computed(() => breakpoints.value.xs.matches)
  const isTablet = computed(() => breakpoints.value.sm.matches || breakpoints.value.md.matches)
  const isDesktop = computed(() => breakpoints.value.lg.matches || breakpoints.value.xl.matches)

  return {
    // 状态
    globalLoading,
    sidebarCollapsed,
    theme,
    language,
    notifications,
    appSettings,
    breakpoints,

    // 计算属性
    isDarkTheme,
    unreadNotifications,
    isMobile,
    isTablet,
    isDesktop,

    // 动作
    setGlobalLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    setLanguage,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    clearAllNotifications,
    updateAppSettings,
    initializeApp,
    showMessage,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
</file>

<file path="frontend/src/stores/auth.js">
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value || {})

  // 动作
  const login = async (credentials) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/auth/login', credentials)
      const { token: newToken, user_id, message } = response.data
      
      // 保存认证信息
      token.value = newToken
      user.value = { user_id, username: credentials.username }
      
      // 持久化存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      ElMessage.success(message || '登录成功')
      
      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      const message = error.response?.data?.detail || '登录失败，请检查用户名和密码'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/auth/register', userData)
      const { token: newToken, user_id, message } = response.data
      
      // 注册成功后自动登录
      token.value = newToken
      user.value = { user_id, username: userData.username, email: userData.email }
      
      // 持久化存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      ElMessage.success(message || '注册成功')
      
      return { success: true }
    } catch (error) {
      console.error('注册失败:', error)
      const message = error.response?.data?.detail || '注册失败，请稍后重试'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // 清除本地状态
      user.value = null
      token.value = null
      
      // 清除持久化存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 清除API默认header
      delete api.defaults.headers.common['Authorization']
      
      ElMessage.success('已安全退出')
      
      return { success: true }
    } catch (error) {
      console.error('退出失败:', error)
      return { success: false }
    }
  }

  const checkAuth = async () => {
    try {
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')
      
      if (!storedToken || !storedUser) {
        return false
      }
      
      // 恢复状态
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
      
      // 验证token有效性（可选）
      // 这里可以调用一个验证接口来确认token是否仍然有效
      
      return true
    } catch (error) {
      console.error('认证检查失败:', error)
      // 清除无效的认证信息
      await logout()
      return false
    }
  }

  const updateUserInfo = (newUserInfo) => {
    user.value = { ...user.value, ...newUserInfo }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  const refreshToken = async () => {
    try {
      // 这里可以实现token刷新逻辑
      // const response = await api.post('/auth/refresh')
      // const { token: newToken } = response.data
      // token.value = newToken
      // localStorage.setItem('token', newToken)
      // api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout()
      return false
    }
  }

  // 初始化时检查认证状态
  const initAuth = async () => {
    await checkAuth()
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 动作
    login,
    register,
    logout,
    checkAuth,
    updateUserInfo,
    refreshToken,
    initAuth
  }
})
</file>

<file path="frontend/src/utils/api.js">
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 显示加载状态
    if (config.showLoading !== false) {
      // 这里可以显示全局loading
    }

    console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    
    return config
  },
  (error) => {
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 隐藏加载状态
    if (response.config.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    console.log('收到响应:', response.status, response.config.url, response.data)
    
    return response
  },
  async (error) => {
    // 隐藏加载状态
    if (error.config?.showLoading !== false) {
      // 这里可以隐藏全局loading
    }

    const { response, config } = error

    // 网络错误
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = response

    // 根据状态码处理不同错误
    switch (status) {
      case 400:
        ElMessage.error(data?.detail || '请求参数错误')
        break
        
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        delete api.defaults.headers.common['Authorization']
        
        ElMessageBox.confirm(
          '登录状态已过期，请重新登录',
          '提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          window.location.href = '/login'
        }).catch(() => {
          // 用户取消
        })
        break
        
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 验证错误
        if (data?.detail && Array.isArray(data.detail)) {
          const errors = data.detail.map(err => err.msg).join(', ')
          ElMessage.error(`数据验证失败: ${errors}`)
        } else {
          ElMessage.error(data?.detail || '数据验证失败')
        }
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
        
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后重试')
        break
        
      default:
        ElMessage.error(data?.detail || `请求失败 (${status})`)
    }

    console.error('请求失败:', status, config?.url, data)
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 认证相关
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh')
  },

  // 人格档案相关
  personalities: {
    list: () => api.get('/personalities'),
    create: (data) => api.post('/personalities', data),
    get: (id) => api.get(`/personalities/${id}`),
    update: (id, data) => api.put(`/personalities/${id}`, data),
    delete: (id) => api.delete(`/personalities/${id}`)
  },

  // 对话相关
  chat: {
    start: (personalityId) => api.post(`/chat/start/${personalityId}`),
    respond: (data) => api.post('/chat/respond', data),
    getHistory: (conversationId) => api.get(`/chat/history/${conversationId}`),
    getConversations: (personalityId) => api.get(`/chat/conversations/${personalityId}`)
  },

  // 预测相关
  prediction: {
    predict: (data) => api.post('/predict', data),
    getHistory: (personalityId) => api.get(`/predictions/${personalityId}`)
  },

  // 验证相关
  validation: {
    validate: (data) => api.post('/validate', data),
    getReports: (personalityId) => api.get(`/validation/reports/${personalityId}`)
  },

  // 分析相关
  analytics: {
    getOverview: () => api.get('/analytics/overview'),
    getPersonalityStats: (personalityId) => api.get(`/analytics/personality/${personalityId}`),
    getProgressReport: (personalityId) => api.get(`/analytics/progress/${personalityId}`)
  },

  // 模拟相关 - 西牟拉胡协议
  simulation: {
    start: (personalityId, initialMessage = '你好') =>
      api.post(`/api/v1/simulation/start/${personalityId}`, {
        personality_id: personalityId,
        initial_message: initialMessage
      }),
    chat: (conversationId, userInput) =>
      api.post(`/api/v1/simulation/chat/${conversationId}`, { user_input: userInput }),
    getConversations: (personalityId) =>
      api.get(`/api/v1/simulation/conversations/${personalityId}`),
    getMessages: (conversationId) =>
      api.get(`/api/v1/simulation/messages/${conversationId}`)
  }
}

// 文件上传
export const uploadFile = (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 下载文件
export const downloadFile = (url, filename) => {
  return api.get(url, {
    responseType: 'blob'
  }).then(response => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}

// 批量请求
export const batchRequest = (requests) => {
  return Promise.allSettled(requests.map(request => api(request)))
}

// 重试机制
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }
}

export default api
</file>

<file path="frontend/src/views/analytics/Analytics.vue">
<template>
  <div class="analytics">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><DataAnalysis /></el-icon>
        <h2>数据分析</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { DataAnalysis } from '@element-plus/icons-vue'
</script>

<style scoped>
.analytics {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
</file>

<file path="frontend/src/views/auth/Login.vue">
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">人格复刻系统</h1>
        <p class="login-subtitle">100% Personality Cloning System</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.isLoading"
            @click="handleLogin"
          >
            {{ authStore.isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>
          还没有账号？
          <router-link to="/register" class="register-link">
            立即注册
          </router-link>
        </p>
      </div>
      
      <!-- 演示账号 -->
      <div class="demo-accounts">
        <el-divider>演示账号</el-divider>
        <el-button
          size="small"
          type="info"
          plain
          @click="fillDemoAccount"
        >
          使用演示账号
        </el-button>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      // 登录成功，跳转到首页
      router.push('/')
    }
  } catch (error) {
    console.error('登录处理失败:', error)
  }
}

// 填充演示账号
const fillDemoAccount = () => {
  loginForm.username = 'demo'
  loginForm.password = 'demo123'
  ElMessage.info('已填充演示账号信息')
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

.register-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link:hover {
  text-decoration: underline;
}

.demo-accounts {
  margin-top: 30px;
  text-align: center;
}

.demo-accounts :deep(.el-divider__text) {
  background-color: rgba(255, 255, 255, 0.95);
  color: #7f8c8d;
  font-size: 12px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .circle-1,
  .circle-2,
  .circle-3 {
    display: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .login-title {
    color: #ecf0f1;
  }
  
  .login-subtitle {
    color: #bdc3c7;
  }
}
</style>
</file>

<file path="frontend/src/views/auth/Register.vue">
<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1 class="register-title">创建账号</h1>
        <p class="register-subtitle">加入人格复刻系统</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱"
            size="large"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleRegister"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-button"
            :loading="authStore.isLoading"
            @click="handleRegister"
          >
            {{ authStore.isLoading ? '注册中...' : '注册' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="register-footer">
        <p>
          已有账号？
          <router-link to="/login" class="login-link">
            立即登录
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const registerFormRef = ref()

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return
    
    const result = await authStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    })
    
    if (result.success) {
      router.push('/')
    }
  } catch (error) {
    console.error('注册处理失败:', error)
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.register-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.register-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.register-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.login-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}
</style>
</file>

<file path="frontend/src/views/chat/ChatInterface.vue">
<template>
  <div class="chat-interface">
    <!-- 人格选择器 -->
    <el-card v-if="!selectedPersonality" class="personality-selector">
      <template #header>
        <h3>选择要对话的人格档案</h3>
      </template>

      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="48"><User /></el-icon>
        <p>暂无人格档案</p>
        <el-button type="primary" @click="createPersonality">
          创建人格档案
        </el-button>
      </div>

      <div v-else class="personality-list">
        <div
          v-for="personality in personalities"
          :key="personality.personality_id"
          class="personality-item"
          @click="selectPersonality(personality)"
        >
          <div class="personality-info">
            <h4>{{ personality.target_name }}</h4>
            <p>{{ personality.description || '暂无描述' }}</p>
            <el-progress
              :percentage="Math.round(personality.completion_percentage || 0)"
              :stroke-width="4"
              :show-text="false"
            />
          </div>
          <el-button type="primary">开始对话</el-button>
        </div>
      </div>
    </el-card>

    <!-- 对话界面 -->
    <div v-else class="chat-container">
      <!-- 对话头部 -->
      <el-card class="chat-header">
        <div class="header-content">
          <div class="personality-info">
            <h3>{{ selectedPersonality.target_name }}</h3>
            <p>完成度: {{ Math.round(selectedPersonality.completion_percentage || 0) }}%</p>
          </div>
          <div class="actions">
            <el-button @click="selectedPersonality = null">
              <el-icon><ArrowLeft /></el-icon>
              返回选择
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 消息列表 -->
      <el-card class="chat-messages" v-loading="loading">
        <div class="messages-container" ref="messagesContainer">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message', message.sender]"
          >
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <div v-if="messages.length === 0" class="empty-messages">
            <el-icon size="48"><ChatDotRound /></el-icon>
            <p>开始与 {{ selectedPersonality.target_name }} 对话吧！</p>
          </div>
        </div>
      </el-card>

      <!-- 输入区域 -->
      <el-card class="chat-input">
        <div class="input-container">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入您的消息..."
            @keydown.ctrl.enter="sendMessage"
          />
          <div class="input-actions">
            <span class="tip">Ctrl + Enter 发送</span>
            <el-button
              type="primary"
              :loading="sending"
              :disabled="!userInput.trim()"
              @click="sendMessage"
            >
              发送
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, User, ArrowLeft } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const personalities = ref([])
const selectedPersonality = ref(null)
const messages = ref([])
const userInput = ref('')
const loading = ref(false)
const sending = ref(false)
const conversationId = ref(null)
const messagesContainer = ref(null)

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const selectPersonality = async (personality) => {
  selectedPersonality.value = personality
  await startConversation()
}

const startConversation = async () => {
  try {
    loading.value = true

    // 使用新的模拟API启动对话
    const response = await apiMethods.simulation.start(
      selectedPersonality.value.personality_id,
      '你好，很高兴认识你！'
    )

    conversationId.value = response.data.conversation_id

    // 添加用户的初始消息和AI的回复
    messages.value = [
      {
        id: Date.now(),
        sender: 'user',
        content: '你好，很高兴认识你！',
        timestamp: new Date()
      },
      {
        id: Date.now() + 1,
        sender: 'ai',
        content: response.data.ai_response,
        timestamp: new Date()
      }
    ]

    await scrollToBottom()
  } catch (error) {
    console.error('启动对话失败:', error)
    ElMessage.error('启动对话失败，请重试')
  } finally {
    loading.value = false
  }
}

const sendMessage = async () => {
  if (!userInput.value.trim() || sending.value) return

  const messageText = userInput.value.trim()
  userInput.value = ''

  // 添加用户消息
  messages.value.push({
    id: Date.now(),
    sender: 'user',
    content: messageText,
    timestamp: new Date()
  })

  await scrollToBottom()

  try {
    sending.value = true

    // 使用新的模拟API发送消息
    const response = await apiMethods.simulation.chat(
      conversationId.value,
      messageText
    )

    // 添加AI回复
    messages.value.push({
      id: Date.now() + 1,
      sender: 'ai',
      content: response.data.ai_response,
      timestamp: new Date()
    })

    await scrollToBottom()
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sending.value = false
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const loadPersonalities = async () => {
  try {
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []

    // 如果URL中有personalityId参数，自动选择
    const personalityId = route.params.personalityId
    if (personalityId) {
      const personality = personalities.value.find(p => p.personality_id === personalityId)
      if (personality) {
        await selectPersonality(personality)
      }
    }
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.chat-interface {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.personality-selector {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.personality-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.personality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personality-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.personality-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-header {
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.message {
  margin-bottom: 20px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
}

.message.user .message-content {
  background: #409eff;
  color: white;
}

.message.ai .message-content {
  background: #f5f7fa;
  color: #303133;
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 5px;
}

.empty-messages {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.chat-input {
  flex-shrink: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tip {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface {
    padding: 10px;
  }

  .message-content {
    max-width: 85%;
  }

  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
</file>

<file path="frontend/src/views/Dashboard.vue">
<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>欢迎使用人格复刻系统</h1>
      <p>通过AI技术实现100%的人格复刻与分析</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPersonalities }}</div>
              <div class="stat-label">人格档案</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalConversations }}</div>
              <div class="stat-label">对话会话</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPredictions }}</div>
              <div class="stat-label">预测分析</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.averageAccuracy }}%</div>
              <div class="stat-label">平均准确度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速开始</span>
            </div>
          </template>
          
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              @click="createPersonality"
            >
              <el-icon><Plus /></el-icon>
              创建人格档案
            </el-button>
            
            <el-button
              type="success"
              size="large"
              @click="startChat"
            >
              <el-icon><ChatDotRound /></el-icon>
              开始对话分析
            </el-button>
            
            <el-button
              type="warning"
              size="large"
              @click="runPrediction"
            >
              <el-icon><TrendCharts /></el-icon>
              运行预测
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          
          <div class="recent-activities">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
            
            <div v-if="recentActivities.length === 0" class="no-activities">
              暂无活动记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统状态 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button type="text" @click="refreshStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">API服务:</span>
              <el-tag :type="systemStatus.api ? 'success' : 'danger'">
                {{ systemStatus.api ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">数据库:</span>
              <el-tag :type="systemStatus.database ? 'success' : 'danger'">
                {{ systemStatus.database ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">AI模型:</span>
              <el-tag :type="systemStatus.aiModel ? 'success' : 'danger'">
                {{ systemStatus.aiModel ? '正常' : '异常' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, ChatDotRound, TrendCharts, CircleCheck, Plus, Refresh } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { apiMethods } from '../utils/api'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const stats = reactive({
  totalPersonalities: 0,
  totalConversations: 0,
  totalPredictions: 0,
  averageAccuracy: 0
})

const recentActivities = ref([])

const systemStatus = reactive({
  api: true,
  database: true,
  aiModel: true
})

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const startChat = () => {
  router.push('/chat')
}

const runPrediction = () => {
  router.push('/prediction')
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const refreshStatus = async () => {
  try {
    // 这里可以调用API检查系统状态
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const loadDashboardData = async () => {
  try {
    // 加载真实的统计数据
    const [personalitiesResponse, analyticsResponse] = await Promise.allSettled([
      apiMethods.personalities.list(),
      // 暂时使用模拟数据，后续实现analytics API
      Promise.resolve({ data: { totalConversations: 0, totalPredictions: 0, averageAccuracy: 0 } })
    ])

    // 处理人格档案数据
    if (personalitiesResponse.status === 'fulfilled') {
      const personalities = personalitiesResponse.value.data || []
      stats.totalPersonalities = personalities.length
    } else {
      stats.totalPersonalities = 0
    }

    // 处理分析数据
    if (analyticsResponse.status === 'fulfilled') {
      const analytics = analyticsResponse.value.data
      stats.totalConversations = analytics.totalConversations || 0
      stats.totalPredictions = analytics.totalPredictions || 0
      stats.averageAccuracy = analytics.averageAccuracy || 0
    }

    // 暂时使用模拟的活动数据，后续从API获取
    recentActivities.value = [
      {
        id: 1,
        title: '系统启动成功',
        time: new Date(),
        icon: 'User'
      }
    ]
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败')
    // 设置默认值
    stats.totalPersonalities = 0
    stats.totalConversations = 0
    stats.totalPredictions = 0
    stats.averageAccuracy = 0
    recentActivities.value = []
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h1 {
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-header p {
  font-size: 16px;
  color: #7f8c8d;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

.quick-actions {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons .el-button {
  justify-content: flex-start;
  height: 50px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #667eea;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

.no-activities {
  text-align: center;
  color: #7f8c8d;
  padding: 40px 0;
}

.system-status {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-weight: 500;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .system-status {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
</file>

<file path="frontend/src/views/error/NotFound.vue">
<template>
  <div class="not-found">
    <div class="error-content">
      <div class="error-code">404</div>
      <h1 class="error-title">页面未找到</h1>
      <p class="error-description">
        抱歉，您访问的页面不存在或已被移除。
      </p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  text-align: center;
  color: white;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 20px;
  opacity: 0.8;
}

.error-title {
  font-size: 32px;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityCreate.vue">
<template>
  <div class="personality-create">
    <div class="page-header">
      <h1>创建人格档案</h1>
      <p>通过AI分析创建详细的人格档案</p>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="目标人物姓名" prop="target_name">
          <el-input
            v-model="form.target_name"
            placeholder="请输入要分析的人物姓名"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="人物描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请简要描述这个人的基本信息、性格特点、兴趣爱好等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            <el-icon><Plus /></el-icon>
            创建档案
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  target_name: '',
  description: ''
})

// 验证规则
const rules = {
  target_name: [
    { required: true, message: '请输入目标人物姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入人物描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const response = await apiMethods.post('/personalities', form)

    ElMessage.success('人格档案创建成功！')
    router.push('/personalities')

  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error(error.response?.data?.detail || '创建失败，请重试')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/personalities')
}
</script>

<style scoped>
.personality-create {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityDetail.vue">
<template>
  <div class="personality-detail" v-loading="loading">
    <div v-if="personality">
      <el-page-header @back="goBack" class="page-header">
        <template #content>
          <span class="text-large font-600 mr-3">{{ personality.target_name }}</span>
        </template>
      </el-page-header>

      <el-card class="description-card">
        <p>{{ personality.description || '暂无详细描述。' }}</p>
      </el-card>

      <el-row :gutter="20">
        <!-- 左侧：人格特质 -->
        <el-col :xs="24" :md="10">
          <el-card>
            <template #header>
              <h3>人格特质</h3>
            </template>
            <!-- 大五人格雷达图 -->
            <div ref="radarChart" style="width: 100%; height: 300px;"></div>
            <!-- 其他核心特质 -->
            <el-descriptions :column="1" border>
              <el-descriptions-item label="依恋类型">
                <el-tag>{{ personality.attachment_style || '未知' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文化背景">
                {{ formatCulturalBackground(personality.cultural_background) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- 右侧：人生故事 -->
        <el-col :xs="24" :md="14">
          <el-card>
            <template #header>
              <h3>人生故事</h3>
            </template>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="关键事件" name="events">
                <el-timeline v-if="personality.events.length > 0">
                  <el-timeline-item
                    v-for="(event, index) in personality.events"
                    :key="index"
                    :timestamp="`年龄: ${event.age}`"
                    placement="top"
                  >
                    <el-card>
                      <h4>{{ event.title }}</h4>
                      <p>{{ event.narrative }}</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
                <el-empty v-else description="暂无关键事件记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="核心信念" name="beliefs">
                <el-collapse v-if="personality.beliefs.length > 0">
                  <el-collapse-item
                    v-for="(belief, index) in personality.beliefs"
                    :key="index"
                    :title="belief.statement"
                  >
                    <div>{{ belief.explanation }}</div>
                  </el-collapse-item>
                </el-collapse>
                <el-empty v-else description="暂无核心信念记录"></el-empty>
              </el-tab-pane>
              <el-tab-pane label="家庭关系" name="family">
                 <div v-if="personality.family_members.length > 0">
                    <div v-for="(member, index) in personality.family_members" :key="index" class="family-member">
                       <strong>{{ member.relationship }}:</strong>
                       <span>{{ JSON.stringify(member.summary) }}</span>
                    </div>
                 </div>
                <el-empty v-else description="暂无家庭关系记录"></el-empty>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-empty v-else-if="!loading" description="未找到该人格档案"></el-empty>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import api from '../../utils/api'; // 使用默认导出的axios实例

echarts.use([TitleComponent, TooltipComponent, LegendComponent, RadarChart, CanvasRenderer]);

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const personality = ref(null);
const activeTab = ref('events');
const radarChart = ref(null);
let myChart = null;

const goBack = () => {
  router.push('/personalities');
};

const formatCulturalBackground = (bg) => {
  if (!bg) return '未知';
  return `${bg.region || ''} ${bg.generation || ''} ${bg.ethnicity || ''}`.trim();
};

const setupRadarChart = () => {
  if (radarChart.value && personality.value) {
    myChart = echarts.init(radarChart.value);
    const bigFive = personality.value.big_five;
    const option = {
      tooltip: {},
      radar: {
        indicator: [
          { name: '开放性 (O)', max: 1 },
          { name: '尽责性 (C)', max: 1 },
          { name: '外向性 (E)', max: 1 },
          { name: '宜人性 (A)', max: 1 },
          { name: '神经质 (N)', max: 1 },
        ],
      },
      series: [
        {
          name: '大五人格',
          type: 'radar',
          data: [
            {
              value: [
                bigFive.openness,
                bigFive.conscientiousness,
                bigFive.extraversion,
                bigFive.agreeableness,
                bigFive.neuroticism,
              ],
              name: personality.value.target_name,
            },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }
};

onMounted(async () => {
  const personalityId = route.params.id;
  if (!personalityId) {
    ElMessage.error('无效的人格档案ID');
    loading.value = false;
    return;
  }
  try {
    const response = await api.get(`/personalities/${personalityId}`);
    personality.value = response.data;
    await nextTick();
    setupRadarChart();
  } catch (error) {
    console.error('加载人格详情失败:', error);
    ElMessage.error('加载详情失败');
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.personality-detail {
  padding: 20px;
}
.page-header {
  margin-bottom: 20px;
}
.description-card {
  margin-bottom: 20px;
}
.family-member {
  margin-bottom: 10px;
  font-size: 14px;
}
</style>
</file>

<file path="frontend/src/views/personality/PersonalityList.vue">
<template>
  <div class="personality-list">
    <div class="page-header">
      <h1>人格档案</h1>
      <el-button type="primary" @click="createPersonality">
        <el-icon><Plus /></el-icon>
        创建新档案
      </el-button>
    </div>

    <el-card v-loading="loading">
      <div v-if="personalities.length === 0" class="empty-state">
        <el-icon size="64"><User /></el-icon>
        <h3>暂无人格档案</h3>
        <p>点击上方按钮创建您的第一个人格档案</p>
        <el-button type="primary" @click="createPersonality">
          <el-icon><Plus /></el-icon>
          立即创建
        </el-button>
      </div>

      <div v-else class="personality-grid">
        <div
          v-for="personality in personalities"
          :key="personality.profile_id"
          class="personality-card"
          @click="viewPersonality(personality.profile_id)"
        >
          <div class="card-header">
            <h3>{{ personality.target_name }}</h3>
            <el-tag :type="getStatusType(personality.completion_percentage)">
              {{ getStatusText(personality.completion_percentage) }}
            </el-tag>
          </div>

          <div class="card-content">
            <p class="description">{{ personality.description || '暂无描述' }}</p>

            <div class="progress-section">
              <div class="progress-label">
                <span>完成度</span>
                <span>{{ Math.round(personality.completion_percentage || 0) }}%</span>
              </div>
              <el-progress
                :percentage="Math.round(personality.completion_percentage || 0)"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
          </div>

          <div class="card-footer">
            <span class="create-time">
              创建于 {{ formatDate(personality.created_at) }}
            </span>
            <div class="actions">
              <el-button
                size="small"
                type="primary"
                @click.stop="startChat(personality.profile_id)"
              >
                开始对话
              </el-button>
              <el-button
                size="small"
                @click.stop="viewPersonality(personality.profile_id)"
              >
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, User } from '@element-plus/icons-vue'
import { apiMethods } from '../../utils/api'

const router = useRouter()
const loading = ref(false)
const personalities = ref([])

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const viewPersonality = (id) => {
  router.push(`/personalities/${id}`)
}

const startChat = (personalityId) => {
  router.push(`/chat/${personalityId}`)
}

const getStatusType = (percentage) => {
  if (percentage >= 80) return 'success'
  if (percentage >= 50) return 'warning'
  return 'info'
}

const getStatusText = (percentage) => {
  if (percentage >= 80) return '已完成'
  if (percentage >= 50) return '进行中'
  return '刚开始'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadPersonalities = async () => {
  try {
    loading.value = true
    const response = await apiMethods.personalities.list()
    personalities.value = response.data || []
  } catch (error) {
    console.error('加载人格档案失败:', error)
    ElMessage.error('加载失败，请重试')
    personalities.value = []
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadPersonalities()
})
</script>

<style scoped>
.personality-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state h3 {
  margin: 20px 0 10px;
  color: #606266;
}

.personality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.personality-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.personality-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.card-content {
  margin-bottom: 15px;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.progress-section {
  margin-bottom: 10px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.actions {
  display: flex;
  gap: 8px;
}
</style>
</file>

<file path="frontend/src/views/prediction/PredictionLab.vue">
<template>
  <div class="prediction-lab">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><TrendCharts /></el-icon>
        <h2>预测实验室</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { TrendCharts } from '@element-plus/icons-vue'
</script>

<style scoped>
.prediction-lab {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
</file>

<file path="frontend/src/views/settings/Settings.vue">
<template>
  <div class="settings">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><Setting /></el-icon>
        <h2>系统设置</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Setting } from '@element-plus/icons-vue'
</script>

<style scoped>
.settings {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
</file>

<file path="frontend/src/views/validation/ValidationCenter.vue">
<template>
  <div class="validation-center">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><CircleCheck /></el-icon>
        <h2>验证中心</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { CircleCheck } from '@element-plus/icons-vue'
</script>

<style scoped>
.validation-center {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
</file>

<file path="frontend/vite.config.js">
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          charts: ['echarts', 'vue-echarts', 'd3'],
        },
      },
    },
  },
})
</file>

<file path="install_dependencies.py">
#!/usr/bin/env python3
"""
智能依赖安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_pip_command(command, description):
    """运行pip命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败:")
        print(f"   错误: {e.stderr}")
        return False

def upgrade_pip():
    """升级pip到最新版本"""
    return run_pip_command(
        [sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
        "升级pip"
    )

def install_requirements(requirements_file):
    """安装requirements文件中的依赖"""
    if not Path(requirements_file).exists():
        print(f"❌ 文件不存在: {requirements_file}")
        return False
    
    return run_pip_command(
        [sys.executable, '-m', 'pip', 'install', '-r', requirements_file],
        f"安装 {requirements_file} 中的依赖"
    )

def install_package(package):
    """安装单个包"""
    return run_pip_command(
        [sys.executable, '-m', 'pip', 'install', package],
        f"安装 {package}"
    )

def main():
    """主函数"""
    print("=" * 50)
    print("📦 智能依赖安装")
    print("=" * 50)
    print()
    
    # 切换到backend目录
    backend_dir = Path('backend')
    if backend_dir.exists():
        os.chdir(backend_dir)
        print(f"📁 切换到目录: {backend_dir.absolute()}")
    else:
        print("❌ backend目录不存在")
        return
    
    # 1. 升级pip
    if not upgrade_pip():
        print("⚠️  pip升级失败，继续尝试安装依赖...")
    
    print()
    
    # 2. 尝试安装完整依赖
    print("🎯 尝试安装完整依赖...")
    if install_requirements('requirements.txt'):
        print("\n🎉 所有依赖安装成功！")
        return
    
    print("\n⚠️  完整依赖安装失败，尝试最小化安装...")
    
    # 3. 尝试安装最小化依赖
    if install_requirements('requirements-minimal.txt'):
        print("\n✅ 最小化依赖安装成功！")
        print("⚠️  注意: 某些高级功能可能不可用")
        return
    
    print("\n❌ 最小化依赖安装也失败，尝试逐个安装核心包...")
    
    # 4. 逐个安装核心包
    core_packages = [
        'fastapi',
        'uvicorn[standard]',
        'pydantic',
        'pydantic-settings',
        'sqlalchemy[asyncio]',
        'aiosqlite',
        'google-generativeai',
        'instructor',
        'python-dotenv',
        'httpx',
        'structlog'
    ]
    
    success_count = 0
    for package in core_packages:
        if install_package(package):
            success_count += 1
        else:
            print(f"⚠️  跳过 {package}")
    
    print(f"\n📊 安装结果: {success_count}/{len(core_packages)} 个核心包安装成功")
    
    if success_count >= 8:  # 至少80%的核心包安装成功
        print("✅ 基本功能应该可以正常工作")
    else:
        print("❌ 安装的包太少，系统可能无法正常工作")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用国内镜像源:")
        print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt")
        print("3. 更新Python到最新版本")

if __name__ == "__main__":
    main()
</file>

<file path="LICENSE">
MIT License

Copyright (c) 2024 100% Personality Cloning System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

IMPORTANT NOTICE:

This software is designed for research and educational purposes. Users are 
responsible for ensuring compliance with applicable laws, regulations, and 
ethical guidelines when using this system for personality analysis and modeling.

The developers of this software do not assume any responsibility for the 
misuse of this technology or any consequences arising from its use.

Please use this technology responsibly and ethically.
</file>

<file path="manual_install.py">
#!/usr/bin/env python3
"""
手动安装核心依赖
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("=" * 50)
    print("📦 手动安装核心依赖")
    print("=" * 50)
    print()
    
    # 切换到backend目录
    backend_dir = Path('backend')
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return
    
    os.chdir(backend_dir)
    print(f"📁 当前目录: {Path.cwd()}")
    
    # 核心包列表
    core_packages = [
        'fastapi',
        'uvicorn[standard]',
        'pydantic',
        'pydantic-settings',
        'sqlalchemy[asyncio]',
        'aiosqlite',
        'google-generativeai',
        'instructor',
        'python-dotenv',
        'httpx',
        'structlog',
        'python-jose[cryptography]',
        'passlib[bcrypt]',
        'python-multipart',
        'python-dateutil',
        'requests',
        'typing-extensions'
    ]
    
    print("🔄 开始安装核心包...")
    print()
    
    success_count = 0
    failed_packages = []
    
    for i, package in enumerate(core_packages, 1):
        print(f"[{i}/{len(core_packages)}] 安装 {package}...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, check=True)
            print(f"✅ {package} 安装成功")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e.stderr.strip()}")
            failed_packages.append(package)
        print()
    
    print("=" * 50)
    print("📊 安装结果")
    print("=" * 50)
    print(f"成功: {success_count}/{len(core_packages)}")
    print(f"失败: {len(failed_packages)}")
    
    if failed_packages:
        print("\n❌ 失败的包:")
        for pkg in failed_packages:
            print(f"  - {pkg}")
    
    if success_count >= 10:  # 至少10个核心包成功
        print("\n✅ 基本功能应该可以正常工作！")
        print("\n🚀 下一步:")
        print("  1. 运行: python simple_main.py")
        print("  2. 或者运行: python start_simple.py")
    else:
        print("\n❌ 安装的包太少，系统可能无法正常工作")
        print("\n💡 建议:")
        print("  1. 检查网络连接")
        print("  2. 更新pip: python -m pip install --upgrade pip")
        print("  3. 使用国内镜像源")

if __name__ == "__main__":
    main()
</file>

<file path="quick_start.py">
#!/usr/bin/env python3
"""
快速启动脚本 - 用于开发和测试
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker已就绪")
            return True
        else:
            print("❌ Docker未安装或不可用")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装")
        return False

def check_port_available(port):
    """检查端口是否可用"""
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result != 0
    except:
        return True

def start_databases():
    """启动数据库服务"""
    print("🔄 检查端口可用性...")

    # 检查关键端口
    ports_to_check = {
        5432: "PostgreSQL",
        7474: "Neo4j",
        6380: "Redis",
        8001: "ChromaDB",
        9200: "Elasticsearch"
    }

    conflicts = []
    for port, service in ports_to_check.items():
        if not check_port_available(port):
            conflicts.append((port, service))

    if conflicts:
        print("⚠️  发现端口冲突:")
        for port, service in conflicts:
            print(f"   - 端口 {port} ({service}) 被占用")
        print()
        print("💡 解决方案:")
        print("   1. 运行 'python fix_ports.py' 自动修复")
        print("   2. 手动停止占用端口的服务")
        print("   3. 修改 docker-compose.yml 中的端口映射")

        response = input("\n是否继续启动？可能会失败 (y/n): ").lower().strip()
        if response != 'y':
            return False

    print("🔄 启动数据库服务...")
    try:
        # 先停止可能存在的容器
        subprocess.run(['docker-compose', 'down'], capture_output=True, text=True)

        # 启动服务
        result = subprocess.run(['docker-compose', 'up', '-d'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 数据库服务启动成功")
            return True
        else:
            print(f"❌ 数据库服务启动失败: {result.stderr}")
            print("\n💡 尝试运行以下命令手动解决:")
            print("   python fix_ports.py")
            return False
    except Exception as e:
        print(f"❌ 启动数据库服务时出错: {e}")
        return False

def wait_for_services():
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    
    services = {
        'PostgreSQL': 'http://localhost:5432',
        'Neo4j': 'http://localhost:7474',
        'ChromaDB': 'http://localhost:8000/api/v1/heartbeat',
        'Redis': 'redis://localhost:6379'
    }
    
    # 简单等待
    time.sleep(15)
    print("✅ 服务启动等待完成")

def check_python_deps():
    """检查Python依赖"""
    backend_dir = Path('backend')
    requirements_file = backend_dir / 'requirements.txt'
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    print("📦 检查Python依赖...")
    
    # 检查虚拟环境
    venv_dir = backend_dir / 'venv'
    if not venv_dir.exists():
        print("🔄 创建虚拟环境...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_dir)], check=True)
            print("✅ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            return False
    
    # 确定pip路径
    if os.name == 'nt':  # Windows
        pip_path = venv_dir / 'Scripts' / 'pip.exe'
        python_path = venv_dir / 'Scripts' / 'python.exe'
    else:  # Unix/Linux/macOS
        pip_path = venv_dir / 'bin' / 'pip'
        python_path = venv_dir / 'bin' / 'python'
    
    # 安装依赖
    print("🔄 安装Python依赖...")
    try:
        subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
        print("✅ Python依赖安装成功")
        return True, python_path
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Python依赖失败: {e}")
        return False, None

def setup_env():
    """设置环境变量"""
    backend_dir = Path('backend')
    env_file = backend_dir / '.env'
    env_example = backend_dir / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("🔄 创建环境变量文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换一些默认值
            content = content.replace('YOUR_GEMINI_API_KEY_HERE', 'demo-key-please-replace')
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 环境变量文件创建成功")
            print("⚠️  请编辑 backend/.env 文件，填入真实的API密钥")
        except Exception as e:
            print(f"❌ 创建环境变量文件失败: {e}")

def start_backend(python_path):
    """启动后端服务"""
    print("🔄 启动后端服务...")
    backend_dir = Path('backend')
    
    try:
        # 初始化数据库
        print("🔄 初始化数据库...")
        subprocess.run([str(python_path), 'init_db.py'], cwd=backend_dir, check=True)
        print("✅ 数据库初始化成功")
        
        # 启动FastAPI服务
        print("🚀 启动FastAPI服务...")
        print("📍 后端服务将在 http://localhost:8000 启动")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 在新的进程中启动服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir)
        
        return True
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return False

def check_node_deps():
    """检查Node.js依赖"""
    frontend_dir = Path('frontend')
    package_json = frontend_dir / 'package.json'
    
    if not package_json.exists():
        print("❌ package.json 文件不存在")
        return False
    
    node_modules = frontend_dir / 'node_modules'
    if not node_modules.exists():
        print("🔄 安装Node.js依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Node.js依赖失败: {e}")
            return False
    
    return True

def start_frontend():
    """启动前端服务"""
    print("🔄 启动前端服务...")
    frontend_dir = Path('frontend')
    
    try:
        print("🚀 启动Vue开发服务器...")
        print("📍 前端服务将在 http://localhost:5173 启动")
        
        # 在新的进程中启动服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir)
        
        return True
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 100% 人格复刻系统 - 快速启动")
    print("=" * 50)
    print()
    
    # 检查Docker
    if not check_docker():
        print("请先安装并启动Docker Desktop")
        return
    
    # 启动数据库
    if not start_databases():
        print("数据库启动失败，请检查Docker配置")
        return
    
    # 等待服务启动
    wait_for_services()
    
    # 设置环境变量
    setup_env()
    
    # 检查并安装Python依赖
    result = check_python_deps()
    if isinstance(result, tuple):
        success, python_path = result
        if not success:
            print("Python环境配置失败")
            return
    else:
        print("Python环境配置失败")
        return
    
    # 启动后端
    if not start_backend(python_path):
        print("后端启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(5)
    
    # 检查并安装Node.js依赖
    if not check_node_deps():
        print("Node.js环境配置失败")
        return
    
    # 启动前端
    if not start_frontend():
        print("前端启动失败")
        return
    
    print()
    print("=" * 50)
    print("🎉 系统启动完成！")
    print("=" * 50)
    print()
    print("📊 服务地址:")
    print("  🌐 前端界面: http://localhost:5173")
    print("  🔧 后端API: http://localhost:8000")
    print("  📚 API文档: http://localhost:8000/docs")
    print("  🗄️  Neo4j浏览器: http://localhost:7474")
    print()
    print("🔑 演示账号:")
    print("  用户名: demo")
    print("  密码: demo123")
    print()
    print("⚠️  重要提醒:")
    print("  1. 请编辑 backend/.env 文件，填入真实的Gemini API密钥")
    print("  2. 首次使用需要注册账号或使用演示账号")
    print("  3. 按 Ctrl+C 可以停止服务")
    print()
    print("📖 详细文档请查看 README.md")

if __name__ == "__main__":
    main()
</file>

<file path="start_simple.py">
#!/usr/bin/env python3
"""
超级简化启动脚本 - 仅核心功能，无编译依赖
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def install_core_dependencies():
    """安装核心依赖"""
    print("🔄 安装核心依赖...")
    backend_dir = Path('backend')
    
    # 检查虚拟环境
    venv_dir = backend_dir / 'venv'
    if not venv_dir.exists():
        print("🔄 创建Python虚拟环境...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_dir)], check=True)
            print("✅ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            return False, None
    
    # 确定Python路径
    if os.name == 'nt':  # Windows
        python_path = venv_dir / 'Scripts' / 'python.exe'
        pip_path = venv_dir / 'Scripts' / 'pip.exe'
    else:  # Unix/Linux/macOS
        python_path = venv_dir / 'bin' / 'python'
        pip_path = venv_dir / 'bin' / 'pip'
    
    # 升级pip
    try:
        subprocess.run([str(pip_path), 'install', '--upgrade', 'pip'], check=True)
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装核心依赖
    core_requirements = backend_dir / 'requirements-core.txt'
    if core_requirements.exists():
        try:
            subprocess.run([str(pip_path), 'install', '-r', str(core_requirements)], check=True)
            print("✅ 核心依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 核心依赖安装失败: {e}")
            return False, None
    
    return True, python_path

def setup_simple_env():
    """设置简化环境"""
    print("🔄 设置简化环境...")
    backend_dir = Path('backend')
    env_file = backend_dir / '.env'
    
    # 创建简化的.env文件
    simple_env_content = """# 简化配置
DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"
GEMINI_API_KEY="your-gemini-api-key-here"
SECRET_KEY="simple-secret-key-for-demo"
DEBUG=true
"""
    
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(simple_env_content)
        print("✅ 环境配置创建成功")
        print("⚠️  请编辑 backend/.env 文件，填入真实的Gemini API密钥")
        return True
    except Exception as e:
        print(f"❌ 环境配置创建失败: {e}")
        return False

def create_simple_main():
    """创建简化的main.py"""
    print("🔄 创建简化的应用...")
    backend_dir = Path('backend')
    simple_main = backend_dir / 'simple_main.py'
    
    simple_main_content = '''"""
简化的FastAPI应用 - 仅核心功能
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="人格复刻系统 - 简化版",
    description="基本功能演示",
    version="1.0.0-simple"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 简单的数据模型
class UserCreate(BaseModel):
    username: str
    email: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class ChatRequest(BaseModel):
    user_input: str

# 模拟数据存储
users_db = {}
conversations_db = {}

@app.get("/")
async def root():
    """健康检查"""
    return {
        "message": "人格复刻系统简化版正在运行",
        "version": "1.0.0-simple",
        "status": "healthy"
    }

@app.post("/auth/register")
async def register_user(user_data: UserCreate):
    """用户注册"""
    if user_data.username in users_db:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    user_id = f"user_{len(users_db) + 1}"
    users_db[user_data.username] = {
        "user_id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "password": user_data.password  # 简化版，实际应该加密
    }
    
    return {
        "message": "注册成功",
        "user_id": user_id,
        "token": user_id
    }

@app.post("/auth/login")
async def login_user(login_data: UserLogin):
    """用户登录"""
    if login_data.username not in users_db:
        raise HTTPException(status_code=401, detail="用户不存在")
    
    user = users_db[login_data.username]
    if user["password"] != login_data.password:
        raise HTTPException(status_code=401, detail="密码错误")
    
    return {
        "message": "登录成功",
        "user_id": user["user_id"],
        "token": user["user_id"]
    }

@app.post("/personalities")
async def create_personality():
    """创建人格档案"""
    personality_id = f"personality_{len(conversations_db) + 1}"
    conversations_db[personality_id] = {
        "personality_id": personality_id,
        "target_name": "演示人格",
        "completion_percentage": 0.0,
        "messages": []
    }
    
    return {
        "message": "人格档案创建成功",
        "personality_id": personality_id,
        "target_name": "演示人格",
        "completion_percentage": 0.0
    }

@app.get("/personalities")
async def list_personalities():
    """列出人格档案"""
    return [
        {
            "personality_id": p["personality_id"],
            "target_name": p["target_name"],
            "completion_percentage": p["completion_percentage"]
        }
        for p in conversations_db.values()
    ]

@app.post("/chat/start/{personality_id}")
async def start_conversation(personality_id: str):
    """开始对话"""
    if personality_id not in conversations_db:
        raise HTTPException(status_code=404, detail="人格档案不存在")
    
    conversation_id = f"conv_{personality_id}_{len(conversations_db[personality_id]['messages']) + 1}"
    
    # 简化的AI响应
    ai_response = "你好！我是人格分析助手。请告诉我关于这个人的一些基本信息，比如他们的性格特点、兴趣爱好或者一些印象深刻的事情。"
    
    conversations_db[personality_id]["messages"].append({
        "sender": "ai",
        "content": ai_response,
        "timestamp": "now"
    })
    
    return {
        "conversation_id": conversation_id,
        "ai_response": ai_response,
        "question_context": "基本信息收集"
    }

@app.post("/chat/respond")
async def respond_to_user(request: ChatRequest):
    """响应用户输入"""
    # 简化的AI响应逻辑
    user_input = request.user_input.lower()
    
    if "性格" in user_input or "特点" in user_input:
        ai_response = "很有趣！你能具体描述一下这些性格特点在日常生活中是如何体现的吗？比如在面对压力或做决定时的表现。"
    elif "兴趣" in user_input or "爱好" in user_input:
        ai_response = "这些兴趣爱好很能反映一个人的内在特质。你觉得这些爱好对他/她的人生观有什么影响吗？"
    elif "工作" in user_input or "职业" in user_input:
        ai_response = "工作环境往往能展现一个人的价值观和处事方式。你能分享一些他/她在工作中的具体表现或故事吗？"
    else:
        ai_response = "谢谢你的分享！这些信息很有价值。你还能告诉我更多关于他/她的情感表达方式或者人际关系处理风格吗？"
    
    return {
        "ai_response": ai_response,
        "analysis_summary": "正在分析用户提供的信息...",
        "confidence_score": 0.75,
        "question_context": "深度探索"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    try:
        with open(simple_main, 'w', encoding='utf-8') as f:
            f.write(simple_main_content)
        print("✅ 简化应用创建成功")
        return True
    except Exception as e:
        print(f"❌ 简化应用创建失败: {e}")
        return False

def start_simple_backend(python_path):
    """启动简化后端"""
    print("🚀 启动简化后端...")
    backend_dir = Path('backend')
    
    try:
        print("📍 后端服务将在 http://localhost:8000 启动")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 启动简化的FastAPI服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                str(python_path), 'simple_main.py'
            ], cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                str(python_path), 'simple_main.py'
            ], cwd=backend_dir)
        
        print("✅ 简化后端启动成功")
        return True
    except Exception as e:
        print(f"❌ 启动简化后端失败: {e}")
        return False

def setup_simple_frontend():
    """设置简化前端"""
    print("🔄 设置前端环境...")
    frontend_dir = Path('frontend')
    
    # 检查node_modules
    node_modules = frontend_dir / 'node_modules'
    if not node_modules.exists():
        print("🔄 安装Node.js依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Node.js依赖失败: {e}")
            print("💡 请确保已安装Node.js，或手动运行: cd frontend && npm install")
            return False
    
    return True

def start_simple_frontend():
    """启动简化前端"""
    print("🚀 启动前端服务...")
    frontend_dir = Path('frontend')
    
    try:
        print("📍 前端服务将在 http://localhost:5173 启动")
        
        # 启动Vue开发服务器
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir)
        
        print("✅ 前端服务启动成功")
        return True
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 人格复刻系统 - 超级简化版")
    print("=" * 60)
    print()
    print("⚠️  注意: 这是演示版本，功能极度简化")
    print("   - 仅包含基本的API接口")
    print("   - 使用内存存储（重启后数据丢失）")
    print("   - AI功能使用模拟响应")
    print("   - 适合快速体验和界面测试")
    print()
    
    # 安装核心依赖
    success, python_path = install_core_dependencies()
    if not success:
        print("❌ 依赖安装失败")
        return
    
    print()
    
    # 设置环境
    if not setup_simple_env():
        print("❌ 环境设置失败")
        return
    
    print()
    
    # 创建简化应用
    if not create_simple_main():
        print("❌ 应用创建失败")
        return
    
    print()
    
    # 启动后端
    if not start_simple_backend(python_path):
        print("❌ 后端启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(3)
    
    # 设置前端
    if setup_simple_frontend():
        # 启动前端
        if start_simple_frontend():
            print()
            print("=" * 60)
            print("🎉 超级简化版启动完成！")
            print("=" * 60)
            print()
            print("📊 服务地址:")
            print("  🌐 前端界面: http://localhost:5173")
            print("  🔧 后端API: http://localhost:8000")
            print("  📚 API文档: http://localhost:8000/docs")
            print()
            print("🧪 测试功能:")
            print("  1. 访问前端界面")
            print("  2. 注册新账号")
            print("  3. 创建人格档案")
            print("  4. 开始对话测试")
            print()
            print("⚠️  重要提醒:")
            print("  - 这是演示版本，数据不会持久化")
            print("  - AI响应是模拟的，不是真实的AI分析")
            print("  - 要使用完整功能，请修复Docker环境")
            print()
            print("🛑 停止服务:")
            print("  关闭打开的控制台窗口")
        else:
            print("❌ 前端启动失败，但后端已启动")
            print("📍 可以直接访问 http://localhost:8000/docs 测试API")
    else:
        print("❌ 前端设置失败，但后端已启动")
        print("📍 可以直接访问 http://localhost:8000/docs 测试API")

if __name__ == "__main__":
    main()
</file>

<file path="start_without_docker.py">
#!/usr/bin/env python3
"""
无Docker启动脚本 - 仅启动后端和前端进行基本测试
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def setup_backend():
    """设置后端环境"""
    print("🔄 设置后端环境...")
    backend_dir = Path('backend')
    
    # 检查虚拟环境
    venv_dir = backend_dir / 'venv'
    if not venv_dir.exists():
        print("🔄 创建Python虚拟环境...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_dir)], check=True)
            print("✅ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            return False, None
    
    # 确定Python路径
    if os.name == 'nt':  # Windows
        python_path = venv_dir / 'Scripts' / 'python.exe'
        pip_path = venv_dir / 'Scripts' / 'pip.exe'
    else:  # Unix/Linux/macOS
        python_path = venv_dir / 'bin' / 'python'
        pip_path = venv_dir / 'bin' / 'pip'
    
    # 安装依赖
    requirements_file = backend_dir / 'requirements.txt'
    if requirements_file.exists():
        print("🔄 安装Python依赖...")
        try:
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
            print("✅ Python依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Python依赖失败: {e}")
            return False, None
    
    # 设置环境变量
    env_file = backend_dir / '.env'
    env_example = backend_dir / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("🔄 创建环境变量文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修改为内存数据库配置（无需Docker）
            content = content.replace(
                'DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"',
                'DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"'
            )
            content = content.replace(
                'REDIS_URL="redis://localhost:6380"',
                'REDIS_URL="memory://"'
            )
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 环境变量文件创建成功")
            print("⚠️  使用SQLite数据库（演示模式）")
        except Exception as e:
            print(f"❌ 创建环境变量文件失败: {e}")
    
    return True, python_path

def start_backend_service(python_path):
    """启动后端服务"""
    print("🚀 启动后端服务...")
    backend_dir = Path('backend')
    
    try:
        print("📍 后端服务将在 http://localhost:8000 启动")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 启动FastAPI服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir)
        
        print("✅ 后端服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return False

def setup_frontend():
    """设置前端环境"""
    print("🔄 设置前端环境...")
    frontend_dir = Path('frontend')
    
    # 检查node_modules
    node_modules = frontend_dir / 'node_modules'
    if not node_modules.exists():
        print("🔄 安装Node.js依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Node.js依赖失败: {e}")
            return False
    
    return True

def start_frontend_service():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    frontend_dir = Path('frontend')
    
    try:
        print("📍 前端服务将在 http://localhost:5173 启动")
        
        # 启动Vue开发服务器
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir)
        
        print("✅ 前端服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 人格复刻系统 - 无Docker启动模式")
    print("=" * 60)
    print()
    print("⚠️  注意: 此模式使用简化配置，仅用于基本功能测试")
    print("   - 使用SQLite替代PostgreSQL")
    print("   - 不包含Neo4j图数据库")
    print("   - 不包含ChromaDB向量数据库")
    print("   - 部分高级功能可能不可用")
    print()
    
    # 设置后端
    success, python_path = setup_backend()
    if not success:
        print("❌ 后端环境设置失败")
        return
    
    print()
    
    # 设置前端
    if not setup_frontend():
        print("❌ 前端环境设置失败")
        return
    
    print()
    
    # 启动后端服务
    if not start_backend_service(python_path):
        print("❌ 后端服务启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(3)
    
    # 启动前端服务
    if not start_frontend_service():
        print("❌ 前端服务启动失败")
        return
    
    print()
    print("=" * 60)
    print("🎉 系统启动完成！")
    print("=" * 60)
    print()
    print("📊 服务地址:")
    print("  🌐 前端界面: http://localhost:5173")
    print("  🔧 后端API: http://localhost:8000")
    print("  📚 API文档: http://localhost:8000/docs")
    print()
    print("🔑 测试账号:")
    print("  可以注册新账号进行测试")
    print()
    print("⚠️  重要提醒:")
    print("  1. 这是简化版本，仅用于基本功能测试")
    print("  2. 要使用完整功能，请修复Docker环境")
    print("  3. 运行 'python check_docker.py' 检查Docker状态")
    print()
    print("🛑 停止服务:")
    print("  关闭打开的控制台窗口或按 Ctrl+C")

if __name__ == "__main__":
    main()
</file>

<file path="start.bat">
@echo off
echo ========================================
echo 100%% 人格复刻系统启动脚本
echo ========================================
echo.

:: 检查Docker是否运行
echo [1/5] 检查Docker状态...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动，请先安装并启动Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker已就绪

:: 启动数据库服务
echo.
echo [2/5] 启动数据库服务...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ 数据库服务启动失败
    pause
    exit /b 1
)
echo ✅ 数据库服务已启动

:: 等待数据库初始化
echo.
echo [3/5] 等待数据库初始化...
timeout /t 10 /nobreak >nul
echo ✅ 数据库初始化完成

:: 检查Python环境
echo.
echo [4/5] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装，请先安装Python 3.10+
    pause
    exit /b 1
)

:: 检查Node.js环境
echo.
echo [5/5] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js 16+
    pause
    exit /b 1
)
echo ✅ Node.js已就绪

echo.
echo ========================================
echo 🚀 系统启动完成！
echo ========================================
echo.
echo 📊 服务状态:
echo   - PostgreSQL: http://localhost:5432
echo   - Neo4j Browser: http://localhost:7474
echo   - ChromaDB: http://localhost:8000
echo   - Redis: localhost:6379
echo   - Elasticsearch: http://localhost:9200
echo.
echo 🔧 下一步操作:
echo   1. 配置后端环境变量 (backend/.env)
echo   2. 启动后端服务: cd backend ^&^& python -m venv venv ^&^& venv\Scripts\activate ^&^& pip install -r requirements.txt ^&^& uvicorn main:app --reload
echo   3. 启动前端服务: cd frontend ^&^& npm install ^&^& npm run dev
echo   4. 访问系统: http://localhost:5173
echo.
echo 📖 详细说明请查看 README.md
echo.
pause
</file>

<file path="start.sh">
#!/bin/bash

echo "========================================"
echo "100% 人格复刻系统启动脚本"
echo "========================================"
echo

# 检查Docker是否运行
echo "[1/5] 检查Docker状态..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker未启动，请先启动Docker"
    exit 1
fi
echo "✅ Docker已就绪"

# 启动数据库服务
echo
echo "[2/5] 启动数据库服务..."
if ! docker-compose up -d; then
    echo "❌ 数据库服务启动失败"
    exit 1
fi
echo "✅ 数据库服务已启动"

# 等待数据库初始化
echo
echo "[3/5] 等待数据库初始化..."
sleep 10
echo "✅ 数据库初始化完成"

# 检查Python环境
echo
echo "[4/5] 检查Python环境..."
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python未安装，请先安装Python 3.10+"
    exit 1
fi
echo "✅ Python已就绪"

# 检查Node.js环境
echo
echo "[5/5] 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 16+"
    exit 1
fi
echo "✅ Node.js已就绪"

echo
echo "========================================"
echo "🚀 系统启动完成！"
echo "========================================"
echo
echo "📊 服务状态:"
echo "  - PostgreSQL: http://localhost:5432"
echo "  - Neo4j Browser: http://localhost:7474"
echo "  - ChromaDB: http://localhost:8000"
echo "  - Redis: localhost:6379"
echo "  - Elasticsearch: http://localhost:9200"
echo
echo "🔧 下一步操作:"
echo "  1. 配置后端环境变量 (backend/.env)"
echo "  2. 启动后端服务: cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt && uvicorn main:app --reload"
echo "  3. 启动前端服务: cd frontend && npm install && npm run dev"
echo "  4. 访问系统: http://localhost:5173"
echo
echo "📖 详细说明请查看 README.md"
echo
</file>

<file path="stop_services.py">
#!/usr/bin/env python3
"""
停止所有服务的脚本
"""

import subprocess
import sys
import os

def stop_docker_services():
    """停止Docker服务"""
    print("🔄 停止数据库服务...")
    try:
        result = subprocess.run(['docker-compose', 'down'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 数据库服务已停止")
            return True
        else:
            print(f"❌ 停止数据库服务失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 停止数据库服务时出错: {e}")
        return False

def kill_processes():
    """终止相关进程"""
    print("🔄 终止应用进程...")
    
    if os.name == 'nt':  # Windows
        # 终止uvicorn进程
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], capture_output=True)
            subprocess.run(['taskkill', '/F', '/IM', 'uvicorn.exe'], capture_output=True)
        except:
            pass
        
        # 终止node进程
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'node.exe'], capture_output=True)
        except:
            pass
    else:  # Unix/Linux/macOS
        # 终止uvicorn进程
        try:
            subprocess.run(['pkill', '-f', 'uvicorn'], capture_output=True)
        except:
            pass
        
        # 终止node进程
        try:
            subprocess.run(['pkill', '-f', 'vite'], capture_output=True)
        except:
            pass
    
    print("✅ 进程终止完成")

def main():
    """主函数"""
    print("=" * 40)
    print("🛑 停止人格复刻系统服务")
    print("=" * 40)
    print()
    
    # 停止Docker服务
    stop_docker_services()
    
    # 终止进程
    kill_processes()
    
    print()
    print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()
</file>

<file path="test_complete_system.py">
#!/usr/bin/env python3
"""
完整系统测试脚本 - 验证西牟拉胡协议的所有功能
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class SystemTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.user_id = None
        self.personality_id = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_user_registration(self):
        """测试用户注册"""
        print("🔐 测试用户注册...")
        
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test123456"
        }
        
        async with self.session.post(f"{BASE_URL}/register", json=user_data) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.auth_token = data.get("access_token")
                print("✅ 用户注册成功")
                return True
            else:
                print(f"❌ 用户注册失败: {resp.status}")
                return False
    
    async def test_user_login(self):
        """测试用户登录"""
        print("🔑 测试用户登录...")
        
        login_data = {
            "username": "test_user",
            "password": "test123456"
        }
        
        async with self.session.post(f"{BASE_URL}/login", data=login_data) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.auth_token = data.get("access_token")
                print("✅ 用户登录成功")
                return True
            else:
                print(f"❌ 用户登录失败: {resp.status}")
                return False
    
    async def test_create_personality(self):
        """测试创建人格档案"""
        print("👤 测试创建人格档案...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        personality_data = {
            "target_name": "测试人格",
            "description": "这是一个用于测试西牟拉胡协议的人格档案"
        }
        
        async with self.session.post(
            f"{BASE_URL}/personalities", 
            json=personality_data, 
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                self.personality_id = data.get("profile_id")
                print(f"✅ 人格档案创建成功: {self.personality_id}")
                return True
            else:
                print(f"❌ 人格档案创建失败: {resp.status}")
                return False
    
    async def test_get_personality_detail(self):
        """测试获取人格档案详情"""
        print("📊 测试获取人格档案详情...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with self.session.get(
            f"{BASE_URL}/personalities/{self.personality_id}", 
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 人格档案详情获取成功: {data['target_name']}")
                print(f"   大五人格: {data['big_five']}")
                return True
            else:
                print(f"❌ 人格档案详情获取失败: {resp.status}")
                return False
    
    async def test_start_simulation(self):
        """测试启动AI模拟对话"""
        print("🤖 测试启动AI模拟对话...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        simulation_data = {
            "personality_id": self.personality_id,
            "initial_message": "你好，很高兴认识你！"
        }
        
        async with self.session.post(
            f"{BASE_URL}/api/v1/simulation/start/{self.personality_id}",
            json=simulation_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                conversation_id = data.get("conversation_id")
                ai_response = data.get("ai_response")
                print(f"✅ AI模拟对话启动成功")
                print(f"   对话ID: {conversation_id}")
                print(f"   AI回复: {ai_response}")
                return conversation_id
            else:
                text = await resp.text()
                print(f"❌ AI模拟对话启动失败: {resp.status}")
                print(f"   错误信息: {text}")
                return None
    
    async def test_continue_simulation(self, conversation_id: str):
        """测试继续AI模拟对话"""
        print("💬 测试继续AI模拟对话...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        message_data = {
            "user_input": "请告诉我你的人生故事"
        }
        
        async with self.session.post(
            f"{BASE_URL}/api/v1/simulation/chat/{conversation_id}",
            json=message_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                ai_response = data.get("ai_response")
                print(f"✅ AI模拟对话继续成功")
                print(f"   AI回复: {ai_response}")
                return True
            else:
                text = await resp.text()
                print(f"❌ AI模拟对话继续失败: {resp.status}")
                print(f"   错误信息: {text}")
                return False
    
    async def test_socratic_analysis(self):
        """测试苏格拉底式分析"""
        print("🧠 测试苏格拉底式分析...")
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        analysis_data = {
            "user_input": "我小时候很喜欢读书，经常一个人安静地看书到很晚。"
        }
        
        async with self.session.post(
            f"{BASE_URL}/chat/start/{self.personality_id}",
            json=analysis_data,
            headers=headers
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ 苏格拉底式分析启动成功")
                print(f"   AI问题: {data.get('ai_response', 'N/A')}")
                return True
            else:
                text = await resp.text()
                print(f"❌ 苏格拉底式分析失败: {resp.status}")
                print(f"   错误信息: {text}")
                return False
    
    async def run_complete_test(self):
        """运行完整的系统测试"""
        print("🚀 开始完整系统测试...\n")
        
        # 测试用户认证
        if not await self.test_user_registration():
            # 如果注册失败，尝试登录
            if not await self.test_user_login():
                print("❌ 用户认证失败，测试终止")
                return False
        
        # 测试人格档案管理
        if not await self.test_create_personality():
            print("❌ 人格档案创建失败，测试终止")
            return False
        
        if not await self.test_get_personality_detail():
            print("❌ 人格档案详情获取失败")
        
        # 测试AI模拟功能
        conversation_id = await self.test_start_simulation()
        if conversation_id:
            await self.test_continue_simulation(conversation_id)
        
        # 测试分析功能
        await self.test_socratic_analysis()
        
        print("\n🎉 系统测试完成！")
        return True

async def main():
    """主函数"""
    async with SystemTester() as tester:
        await tester.run_complete_test()

if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="test_demo_login.py">
#!/usr/bin/env python3
"""
测试演示账号登录
"""

import requests
import json

def test_demo_login():
    """测试演示账号登录"""
    print("🔍 测试演示账号登录...")
    
    try:
        # 测试登录
        login_data = {
            "username": "demo",
            "password": "demo123"
        }
        
        response = requests.post(
            "http://localhost:8000/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 演示账号登录成功！")
            print(f"   用户ID: {result.get('user_id')}")
            print(f"   Token: {result.get('token')[:20]}...")
            
            # 测试获取人格档案
            token = result.get('token')
            headers = {"Authorization": f"Bearer {token}"}
            
            personalities_response = requests.get(
                "http://localhost:8000/personalities",
                headers=headers,
                timeout=10
            )
            
            if personalities_response.status_code == 200:
                personalities = personalities_response.json()
                print(f"✅ 获取人格档案成功，共 {len(personalities)} 个档案")
                
                for p in personalities:
                    print(f"   - {p.get('target_name')}: {p.get('completion_percentage', 0):.1f}%")
            else:
                print(f"❌ 获取人格档案失败: {personalities_response.status_code}")
                
        else:
            print(f"❌ 演示账号登录失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_demo_login()
</file>

<file path="test_dynamic_heartbeat.py">
#!/usr/bin/env python3
"""
测试动态心跳功能的脚本
验证AI的情绪和关系状态是否能根据用户输入动态变化
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"

async def test_dynamic_heartbeat():
    """测试动态心跳功能"""
    
    async with aiohttp.ClientSession() as session:
        print("🧪 开始测试动态心跳功能...")
        
        # 1. 获取第一个人格档案
        async with session.get(f"{BASE_URL}/personalities") as resp:
            personalities = await resp.json()
            if not personalities:
                print("❌ 没有找到人格档案，请先创建一个")
                return
            
            personality_id = personalities[0]["profile_id"]
            personality_name = personalities[0]["target_name"]
            print(f"📋 使用人格档案: {personality_name} ({personality_id})")
        
        # 2. 启动对话
        start_data = {
            "personality_id": personality_id,
            "initial_message": "你好"
        }
        
        async with session.post(f"{BASE_URL}/simulation/start/{personality_id}", json=start_data) as resp:
            if resp.status != 200:
                print(f"❌ 启动对话失败: {resp.status}")
                return
            
            result = await resp.json()
            conversation_id = result["conversation_id"]
            print(f"✅ 对话启动成功: {conversation_id}")
            print(f"🤖 AI初始回复: {result['ai_response']}")
        
        # 3. 测试正面情绪输入
        print("\n🌟 测试正面情绪输入...")
        positive_messages = [
            "你太棒了！我很喜欢和你聊天",
            "感谢你的回复，真的很开心",
            "你说得太好了，我觉得你很赞"
        ]
        
        for msg in positive_messages:
            chat_data = {"user_input": msg}
            async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"👤 用户: {msg}")
                    print(f"🤖 AI: {result['ai_response']}")
                    print("---")
                else:
                    print(f"❌ 发送消息失败: {resp.status}")
        
        # 4. 测试负面情绪输入
        print("\n😔 测试负面情绪输入...")
        negative_messages = [
            "我觉得很失望，这样不好",
            "你说的话让我很生气",
            "这太糟糕了，我讨厌这样"
        ]
        
        for msg in negative_messages:
            chat_data = {"user_input": msg}
            async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"👤 用户: {msg}")
                    print(f"🤖 AI: {result['ai_response']}")
                    print("---")
                else:
                    print(f"❌ 发送消息失败: {resp.status}")
        
        # 5. 再次测试正面输入，看是否能恢复
        print("\n🌈 测试情绪恢复...")
        recovery_msg = "对不起刚才的话，你其实很好，我很感谢你"
        chat_data = {"user_input": recovery_msg}
        async with session.post(f"{BASE_URL}/simulation/chat/{conversation_id}", json=chat_data) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"👤 用户: {recovery_msg}")
                print(f"🤖 AI: {result['ai_response']}")
            else:
                print(f"❌ 发送消息失败: {resp.status}")
        
        print("\n✅ 动态心跳测试完成！")
        print("💡 观察AI的回复是否体现了情绪和关系的变化")

if __name__ == "__main__":
    asyncio.run(test_dynamic_heartbeat())
</file>

<file path="test_system.py">
#!/usr/bin/env python3
"""
系统测试脚本
"""

import requests
import time
import json

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端服务...")
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False

def test_user_registration():
    """测试用户注册"""
    print("🔍 测试用户注册...")
    try:
        test_user = {
            "username": f"test_user_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "test123456"
        }
        
        response = requests.post(
            "http://localhost:8000/auth/register",
            json=test_user,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 用户注册成功")
            data = response.json()
            return data.get("token")
        else:
            print(f"❌ 用户注册失败: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 注册请求失败: {e}")
        return None

def test_personality_creation(token):
    """测试人格档案创建"""
    print("🔍 测试人格档案创建...")
    try:
        personality_data = {
            "target_name": "测试人格",
            "description": "这是一个测试用的人格档案"
        }
        
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            "http://localhost:8000/personalities",
            json=personality_data,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 人格档案创建成功")
            data = response.json()
            return data.get("personality_id")
        else:
            print(f"❌ 人格档案创建失败: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建请求失败: {e}")
        return None

def test_chat_start(personality_id, token):
    """测试对话开始"""
    print("🔍 测试对话开始...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"http://localhost:8000/chat/start/{personality_id}",
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            print("✅ 对话开始成功")
            data = response.json()
            return data.get("conversation_id")
        else:
            print(f"❌ 对话开始失败: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 对话请求失败: {e}")
        return None

def test_frontend():
    """测试前端服务"""
    print("🔍 测试前端服务...")
    try:
        response = requests.get("http://localhost:5173/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接前端服务: {e}")
        return False

def test_databases():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 测试Neo4j
    try:
        response = requests.get("http://localhost:7474/", timeout=5)
        if response.status_code == 200:
            print("✅ Neo4j服务正常")
        else:
            print("❌ Neo4j服务异常")
    except:
        print("❌ 无法连接Neo4j服务")
    
    # 测试ChromaDB
    try:
        response = requests.get("http://localhost:8001/api/v1/heartbeat", timeout=5)
        if response.status_code == 200:
            print("✅ ChromaDB服务正常")
        elif response.status_code in [410, 501]:  # API deprecated or unimplemented
            print("✅ ChromaDB服务正常 (API v2)")
        else:
            print(f"❌ ChromaDB服务异常: {response.status_code}")
    except Exception as e:
        # 尝试连接根路径
        try:
            response = requests.get("http://localhost:8001/", timeout=5)
            if response.status_code == 200:
                print("✅ ChromaDB服务正常")
            else:
                print("❌ 无法连接ChromaDB服务")
        except:
            print("❌ 无法连接ChromaDB服务")

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 人格复刻系统测试")
    print("=" * 50)
    print()
    
    # 测试数据库
    test_databases()
    print()
    
    # 测试后端
    if not test_backend_health():
        print("❌ 后端服务未启动，请先启动后端服务")
        return
    print()
    
    # 测试用户注册
    token = test_user_registration()
    if not token:
        print("❌ 用户注册失败，无法继续测试")
        return
    print()
    
    # 测试人格档案创建
    personality_id = test_personality_creation(token)
    if not personality_id:
        print("❌ 人格档案创建失败")
        return
    print()
    
    # 测试对话开始
    conversation_id = test_chat_start(personality_id, token)
    if conversation_id:
        print("✅ 对话功能正常")
    else:
        print("⚠️  对话功能可能需要配置API密钥")
    print()
    
    # 测试前端
    test_frontend()
    print()
    
    print("=" * 50)
    print("🎉 系统测试完成！")
    print("=" * 50)
    print()
    print("📊 测试结果总结:")
    print("  - 后端API: ✅ 正常")
    print("  - 用户认证: ✅ 正常")
    print("  - 人格档案: ✅ 正常")
    print("  - 对话功能: ⚠️  需要API密钥")
    print("  - 前端界面: ✅ 正常")
    print()
    print("🔧 下一步:")
    print("  1. 配置Gemini API密钥 (backend/.env)")
    print("  2. 访问前端界面: http://localhost:5173")
    print("  3. 使用演示账号或注册新账号")

if __name__ == "__main__":
    main()
</file>

</files>
