"""
Simulation API endpoints - 西牟拉胡协议的API接口
提供AI角色扮演和人格模拟功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel
from typing import List, Optional

from app.database.db_session import get_db_session
from app.database.models import User, PersonalityProfile, Conversation, Message
from app.services.personality_simulator import PersonalitySimulator
import structlog

logger = structlog.get_logger()

router = APIRouter()
simulator = PersonalitySimulator()

# === Pydantic Models ===

class SimulationRequest(BaseModel):
    user_input: str
    conversation_id: Optional[str] = None

class SimulationResponse(BaseModel):
    ai_response: str
    conversation_id: str
    personality_name: str
    response_metadata: Optional[dict] = None

class StartSimulationRequest(BaseModel):
    personality_id: str
    initial_message: Optional[str] = "你好"

# === Helper Functions ===

async def get_current_user_simple(db: AsyncSession) -> User:
    """简化的用户获取（用于测试）"""
    # 这里应该实现真正的用户认证
    # 暂时返回第一个用户用于测试
    result = await db.execute(select(User).limit(1))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No user found. Please register first."
        )
    return user

# === API Endpoints ===

@router.post("/simulation/start/{personality_id}", response_model=SimulationResponse)
async def start_personality_simulation(
    personality_id: str,
    request: StartSimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    启动与指定人格的模拟对话
    这是西牟拉胡协议的入口点
    """
    try:
        # 获取用户（简化版本）
        user = await get_current_user_simple(db)
        
        # 验证人格档案是否存在
        result = await db.execute(
            select(PersonalityProfile).where(PersonalityProfile.profile_id == personality_id)
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 创建新的对话会话
        conversation = Conversation(
            user_id=user.user_id,
            personality_id=personality.profile_id
        )
        
        db.add(conversation)
        await db.commit()
        await db.refresh(conversation)
        
        # 生成AI的初始回复
        ai_response = await simulator.generate_response(
            personality_id=personality_id,
            user_input=request.initial_message,
            db=db,
            conversation_id=str(conversation.conversation_id), # 新增这一行
            conversation_history=[]
        )
        
        # 保存初始消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.initial_message
        )
        
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(user_message)
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Started personality simulation",
            personality_id=personality_id,
            conversation_id=str(conversation.conversation_id),
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=str(conversation.conversation_id),
            personality_name=personality.target_name,
            response_metadata={
                "completion_percentage": personality.completion_percentage,
                "attachment_style": personality.attachment_style
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start simulation"
        )

@router.post("/simulation/chat/{conversation_id}", response_model=SimulationResponse)
async def continue_personality_simulation(
    conversation_id: str,
    request: SimulationRequest,
    db: AsyncSession = Depends(get_db_session)
):
    """
    继续与人格的模拟对话
    """
    try:
        # 获取对话会话
        result = await db.execute(
            select(Conversation).where(Conversation.conversation_id == conversation_id)
        )
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # 获取人格档案
        result = await db.execute(
            select(PersonalityProfile).where(
                PersonalityProfile.profile_id == conversation.personality_id
            )
        )
        personality = result.scalar_one_or_none()
        
        if not personality:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Personality profile not found"
            )
        
        # 获取对话历史
        history_result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation.conversation_id)
            .order_by(Message.timestamp)
        )
        messages = history_result.scalars().all()
        conversation_history = [f"{msg.sender}: {msg.content}" for msg in messages]
        
        # 保存用户消息
        user_message = Message(
            conversation_id=conversation.conversation_id,
            sender="user",
            content=request.user_input
        )
        
        db.add(user_message)
        await db.commit()
        
        # 生成AI回复
        ai_response = await simulator.generate_response(
            personality_id=str(conversation.personality_id),
            user_input=request.user_input,
            db=db,
            conversation_id=conversation_id, # 新增这一行
            conversation_history=conversation_history
        )
        
        # 保存AI回复
        ai_message = Message(
            conversation_id=conversation.conversation_id,
            sender="ai",
            content=ai_response
        )
        
        db.add(ai_message)
        await db.commit()
        
        logger.info(
            "Continued personality simulation",
            conversation_id=conversation_id,
            target_name=personality.target_name
        )
        
        return SimulationResponse(
            ai_response=ai_response,
            conversation_id=conversation_id,
            personality_name=personality.target_name,
            response_metadata={
                "message_count": len(conversation_history) + 2,
                "completion_percentage": personality.completion_percentage
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to continue personality simulation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to continue simulation"
        )

@router.get("/simulation/conversations/{personality_id}")
async def get_personality_conversations(
    personality_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定人格的所有对话会话"""
    try:
        result = await db.execute(
            select(Conversation).where(Conversation.personality_id == personality_id)
        )
        conversations = result.scalars().all()
        
        return [
            {
                "conversation_id": str(conv.conversation_id),
                "started_at": conv.started_at.isoformat() if conv.started_at else None,
                "ended_at": conv.ended_at.isoformat() if conv.ended_at else None
            }
            for conv in conversations
        ]
        
    except Exception as e:
        logger.error("Failed to get personality conversations", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversations"
        )

@router.get("/simulation/messages/{conversation_id}")
async def get_conversation_messages(
    conversation_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取指定对话的所有消息"""
    try:
        result = await db.execute(
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .order_by(Message.timestamp)
        )
        messages = result.scalars().all()
        
        return [
            {
                "message_id": str(msg.message_id),
                "sender": msg.sender,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None
            }
            for msg in messages
        ]
        
    except Exception as e:
        logger.error("Failed to get conversation messages", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get messages"
        )
