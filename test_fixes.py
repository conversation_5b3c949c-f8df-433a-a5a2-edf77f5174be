#!/usr/bin/env python3
"""
测试修复后的系统核心功能
验证JWT认证、角色生成和AI对话是否正常工作
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_jwt_functions():
    """测试JWT功能"""
    print("🔐 测试JWT认证功能...")
    
    try:
        from main import create_access_token, SECRET_KEY, ALGORITHM
        from jose import jwt
        from datetime import timedelta
        
        # 测试创建token
        test_data = {"sub": "demo", "user_id": "test-id"}
        token = create_access_token(test_data, timedelta(minutes=30))
        
        # 测试解析token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        if payload.get("sub") == "demo":
            print("✅ JWT认证功能正常")
            return True
        else:
            print("❌ JWT解析失败")
            return False
            
    except Exception as e:
        print(f"❌ JWT测试失败: {e}")
        return False

async def test_character_generator():
    """测试角色生成器"""
    print("\n🎭 测试角色生成器...")
    
    try:
        # 检查instructor是否可用
        import instructor
        import google.generativeai as genai
        
        # 检查API密钥
        from dotenv import load_dotenv
        load_dotenv("backend/.env")
        
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
            print("⚠️  Gemini API Key未配置，跳过角色生成测试")
            return False
        
        print("✅ 角色生成器依赖检查通过")
        print("💡 提示: 运行 'python backend/character_generator.py' 进行完整测试")
        return True
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False
    except Exception as e:
        print(f"❌ 角色生成器测试失败: {e}")
        return False

async def test_personality_simulator():
    """测试人格模拟器"""
    print("\n🤖 测试人格模拟器...")
    
    try:
        from app.services.personality_simulator import PersonalitySimulator
        
        simulator = PersonalitySimulator()
        
        if simulator.available:
            print("✅ 人格模拟器初始化成功")
            return True
        else:
            print("⚠️  人格模拟器初始化成功，但LLM不可用")
            print("💡 请检查GEMINI_API_KEY环境变量")
            return False
            
    except Exception as e:
        print(f"❌ 人格模拟器测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "backend/main.py",
        "backend/character_generator.py",
        "backend/batch_character_generator.py",
        "backend/character_manager.py",
        "backend/app/services/personality_simulator.py",
        "backend/requirements.txt",
        "docker-compose.yml",
        "start_optimized.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ 核心文件结构完整")
        return True
    else:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("\n📦 测试Python依赖...")
    
    required_packages = [
        "fastapi",
        "sqlalchemy",
        "pydantic", 
        "google-generativeai",
        "instructor",
        "python-jose",
        "structlog"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if not missing_packages:
        print("✅ 核心依赖项完整")
        return True
    else:
        print("❌ 缺少以下依赖:")
        for package in missing_packages:
            print(f"   - {package}")
        print("💡 运行: pip install -r backend/requirements.txt")
        return False

async def main():
    """主函数"""
    print("🧪 系统修复验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 文件结构测试
    test_results.append(("文件结构", test_file_structure()))
    
    # 2. 依赖项测试
    test_results.append(("依赖项", test_dependencies()))
    
    # 3. JWT功能测试
    test_results.append(("JWT认证", await test_jwt_functions()))
    
    # 4. 人格模拟器测试
    test_results.append(("人格模拟器", await test_personality_simulator()))
    
    # 5. 角色生成器测试
    test_results.append(("角色生成器", await test_character_generator()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有修复验证通过！系统可以正常运行")
        print("\n📚 下一步操作:")
        print("1. 启动系统: python start_optimized.py")
        print("2. 生成角色: cd backend && python batch_character_generator.py")
        print("3. 测试对话: cd backend && python character_manager.py")
        return True
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，请先解决上述问题")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
